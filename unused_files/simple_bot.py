"""
Simple Trading Bot without complex Telegram framework
"""
import asyncio
import time
from decimal import Decimal
from exchanges.manager import ExchangeManager
from strategies.manager import StrategyManager
from analysis.market_analyzer import MarketAnalyzer
from config import get_settings
from loguru import logger

class SimpleTradingBot:
    """Simple trading bot that runs without Telegram interface"""
    
    def __init__(self):
        self.settings = get_settings()
        self.exchange_manager = None
        self.strategy_manager = None
        self.market_analyzer = None
        self.running = False
    
    async def initialize(self):
        """Initialize all components"""
        logger.info("🚀 Initializing Simple Trading Bot...")
        
        # Initialize exchange manager
        self.exchange_manager = ExchangeManager()
        
        # Connect to exchanges
        logger.info("Connecting to exchanges...")
        connection_results = await self.exchange_manager.connect_all()
        
        for exchange, connected in connection_results.items():
            if connected:
                logger.info(f"✅ Connected to {exchange}")
            else:
                logger.error(f"❌ Failed to connect to {exchange}")
        
        if not any(connection_results.values()):
            logger.error("No exchanges connected. Exiting...")
            return False
        
        # Initialize strategy manager
        self.strategy_manager = StrategyManager(self.exchange_manager)
        
        # Initialize market analyzer
        self.market_analyzer = MarketAnalyzer(self.exchange_manager)
        
        logger.info("✅ All components initialized")
        return True
    
    async def run_console_interface(self):
        """Run a simple console interface"""
        logger.info("🎮 Starting console interface...")
        logger.info("Available commands:")
        logger.info("  'balance' - Show portfolio balances")
        logger.info("  'price BTC/USDT' - Show price for symbol")
        logger.info("  'analysis' - Show market analysis")
        logger.info("  'strategies' - Show strategy status")
        logger.info("  'positions' - Show active positions")
        logger.info("  'start' - Start automated trading")
        logger.info("  'stop' - Stop automated trading")
        logger.info("  'quit' - Exit bot")
        logger.info("=" * 50)
        
        while True:
            try:
                command = input("\n💰 Trading Bot > ").strip().lower()
                
                if command == 'quit':
                    break
                elif command == 'balance':
                    await self._show_balance()
                elif command.startswith('price '):
                    symbol = command.split(' ', 1)[1].upper()
                    await self._show_price(symbol)
                elif command == 'analysis':
                    await self._show_analysis()
                elif command == 'strategies':
                    await self._show_strategies()
                elif command == 'positions':
                    await self._show_positions()
                elif command == 'start':
                    await self._start_trading()
                elif command == 'stop':
                    await self._stop_trading()
                else:
                    print("❌ Unknown command. Type 'quit' to exit.")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                logger.error(f"Error processing command: {e}")
    
    async def _show_balance(self):
        """Show portfolio balances"""
        try:
            print("💰 Getting balances...")
            all_balances = await self.exchange_manager.get_all_balances()
            
            print("\n📊 Portfolio Balances:")
            for exchange_name, balances in all_balances.items():
                print(f"\n{exchange_name.upper()}:")
                
                if not balances:
                    print("  No balances found")
                    continue
                
                non_zero_balances = {k: v for k, v in balances.items() if v.total > 0}
                
                if not non_zero_balances:
                    print("  No balances > 0")
                    continue
                
                for currency, balance in sorted(non_zero_balances.items()):
                    print(f"  {currency}: {balance.total:.8f}")
                    if balance.used > 0:
                        print(f"    (Used: {balance.used:.8f})")
                        
        except Exception as e:
            print(f"❌ Error getting balances: {e}")
    
    async def _show_price(self, symbol: str):
        """Show price for symbol"""
        try:
            print(f"📊 Getting price for {symbol}...")
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)
            
            if not tickers:
                print(f"❌ No price data found for {symbol}")
                return
            
            print(f"\n💰 Prices for {symbol}:")
            for exchange_name, ticker in tickers.items():
                print(f"  {exchange_name.upper()}: ${ticker.last:.8f}")
                print(f"    Bid: ${ticker.bid:.8f} | Ask: ${ticker.ask:.8f}")
                print(f"    24h High: ${ticker.high:.8f} | Low: ${ticker.low:.8f}")
                
        except Exception as e:
            print(f"❌ Error getting price: {e}")
    
    async def _show_analysis(self):
        """Show market analysis"""
        try:
            if not self.market_analyzer:
                print("❌ Market analyzer not available")
                return
            
            print("📊 Getting market analysis...")
            
            # Run one analysis cycle if no data
            latest = self.market_analyzer.get_latest_analysis()
            if not latest:
                await self.market_analyzer._perform_market_analysis()
                latest = self.market_analyzer.get_latest_analysis()
            
            if latest:
                print("\n📈 Market Analysis:")
                for symbol, analysis in latest.items():
                    market_data = analysis.get('market_data', {})
                    alerts = analysis.get('alerts', [])
                    
                    print(f"\n{symbol}:")
                    print(f"  Price: ${market_data.get('price', 0):.2f}")
                    print(f"  24h Change: {market_data.get('price_change_24h', 0):.2f}%")
                    print(f"  Alerts: {len(alerts)}")
                    
                    for alert in alerts[:3]:  # Show first 3 alerts
                        severity = alert.get('severity', 'low')
                        emoji = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(severity, '⚪')
                        print(f"    {emoji} {alert.get('message', '')}")
            else:
                print("❌ No analysis data available")
                
        except Exception as e:
            print(f"❌ Error getting analysis: {e}")
    
    async def _show_strategies(self):
        """Show strategy status"""
        try:
            if not self.strategy_manager:
                print("❌ Strategy manager not available")
                return
            
            status = self.strategy_manager.get_strategy_status()
            
            print("\n🤖 Trading Strategies:")
            for name, info in status.items():
                enabled = "✅" if info['enabled'] else "❌"
                active = "🟢" if info['active'] else "🔴"
                
                print(f"\n{enabled} {name}:")
                print(f"  Status: {active} {'Active' if info['active'] else 'Inactive'}")
                print(f"  Positions: {info['positions']}")
                print(f"  Timeframe: {info['timeframe']}")
                print(f"  Risk: {info['risk_percentage']}%")
                
        except Exception as e:
            print(f"❌ Error getting strategies: {e}")
    
    async def _show_positions(self):
        """Show active positions"""
        try:
            if not self.strategy_manager:
                print("❌ Strategy manager not available")
                return
            
            positions = self.strategy_manager.get_all_positions()
            
            if not positions:
                print("📊 No active positions")
                return
            
            print("\n📊 Active Positions:")
            for position_key, position in positions.items():
                strategy_name = position_key.split('_')[0]
                pnl_emoji = "📈" if position.pnl_percentage > 0 else "📉" if position.pnl_percentage < 0 else "➡️"
                
                print(f"\n{position.symbol} ({strategy_name}):")
                print(f"  Side: {position.side.upper()}")
                print(f"  Entry: ${position.entry_price:.4f}")
                print(f"  Current: ${position.current_price:.4f}")
                print(f"  {pnl_emoji} PnL: {position.pnl_percentage:+.2f}%")
                
                if position.stop_loss:
                    print(f"  Stop Loss: ${position.stop_loss:.4f}")
                if position.take_profit:
                    print(f"  Take Profit: ${position.take_profit:.4f}")
                    
        except Exception as e:
            print(f"❌ Error getting positions: {e}")
    
    async def _start_trading(self):
        """Start automated trading"""
        try:
            if not self.strategy_manager:
                print("❌ Strategy manager not available")
                return
            
            print("🤖 Starting automated trading...")
            
            # Start market analyzer
            if self.market_analyzer and not self.running:
                asyncio.create_task(self.market_analyzer.start_analysis())
            
            # Start strategy manager
            asyncio.create_task(self.strategy_manager.start_automated_trading())
            
            self.running = True
            print("✅ Automated trading started!")
            
        except Exception as e:
            print(f"❌ Error starting trading: {e}")
    
    async def _stop_trading(self):
        """Stop automated trading"""
        try:
            if not self.strategy_manager:
                print("❌ Strategy manager not available")
                return
            
            print("🛑 Stopping automated trading...")
            
            # Stop components
            if self.market_analyzer:
                self.market_analyzer.stop_analysis()
            
            if self.strategy_manager:
                self.strategy_manager.stop_automated_trading()
            
            self.running = False
            print("✅ Automated trading stopped!")
            
        except Exception as e:
            print(f"❌ Error stopping trading: {e}")

async def main():
    """Main function"""
    bot = SimpleTradingBot()
    
    try:
        # Initialize
        if not await bot.initialize():
            return
        
        # Run console interface
        await bot.run_console_interface()
        
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Error: {e}")
    finally:
        # Cleanup
        if bot.market_analyzer:
            bot.market_analyzer.stop_analysis()
        if bot.strategy_manager:
            bot.strategy_manager.stop_automated_trading()

if __name__ == "__main__":
    asyncio.run(main())
