"""
Advanced Backtesting Module
"""
import pandas as pd
import numpy as np
from typing import Dict, List
from decimal import Decimal
from loguru import logger
from datetime import datetime, timedelta
from strategies.base import BaseStrategy, TradingSignal

class BacktestEngine:
    def __init__(self, initial_capital: float = 10000.0, use_ai_stop: bool = True):
        self.initial_capital = initial_capital
        self.capital = initial_capital
        self.position = 0
        self.trades = []
        self.trade_history = []
        self.use_ai_stop = use_ai_stop  # Schakelaar voor AI stop-loss/trailing

    def _ai_stop_loss(self, market_data: Dict, signal: TradingSignal) -> float:
        """Bepaal dynamisch een stop-loss op basis van volatiliteit en AI confidence"""
        # Gebruik ATR, volatiliteit of AI confidence indien beschikbaar
        price = market_data['price']
        high = market_data.get('high_24h', price)
        low = market_data.get('low_24h', price)
        volatility = (high - low) / price
        base_stop = 0.015  # 1.5% standaard
        # Dynamisch: meer volatiliteit = ruimere stop
        stop_pct = max(base_stop, min(0.05, volatility * 0.7))
        # Indien AI confidence beschikbaar, pas aan
        ai_conf = getattr(signal, 'confidence', None)
        if ai_conf is not None:
            stop_pct = stop_pct * (1.2 - min(ai_conf, 1.0))  # Meer vertrouwen = strakkere stop
        return price * (1 - stop_pct) if signal.signal in ['strong_buy', 'buy'] else price * (1 + stop_pct)

    def _ai_trailing_stop(self, entry_price: float, current_price: float, best_price: float, base_trail: float = 0.01) -> float:
        """Bepaal trailing stop-loss op basis van beste prijs en volatiliteit"""
        # Trailing stop volgt de beste prijs met een percentage
        trail_pct = base_trail
        trailing_stop = best_price * (1 - trail_pct)
        # Zorg dat trailing stop nooit onder initiële stop-loss komt
        return max(trailing_stop, entry_price * (1 - trail_pct))

    async def run_backtest(self, strategy: BaseStrategy, historical_data: pd.DataFrame) -> Dict:
        """Run backtest with advanced metrics"""
        try:
            results = {
                'trades': [],
                'metrics': {},
                'equity_curve': []
            }
            
            for index, row in historical_data.iterrows():
                market_data = {
                    'price': float(row['close']),
                    'high_24h': float(row['high']),
                    'low_24h': float(row['low']),
                    'volume': float(row['volume']),
                    'timestamp': index
                }
                
                # Get strategy signal
                signal = await strategy.analyze(row['symbol'], market_data)
                
                # Execute trade
                if signal.signal in ['strong_buy', 'buy'] and self.position <= 0:
                    # Calculate position size based on risk
                    risk_per_trade = self.capital * 0.02  # 2% risk per trade
                    if self.use_ai_stop:
                        stop_loss = self._ai_stop_loss(market_data, signal)
                    else:
                        stop_loss = signal.stop_loss if signal.stop_loss else market_data['price'] * 0.98
                    position_size = risk_per_trade / (market_data['price'] - stop_loss)
                    
                    # Open long position
                    self.position = position_size
                    entry_price = market_data['price']
                    self.trades.append({
                        'type': 'buy',
                        'price': entry_price,
                        'size': position_size,
                        'timestamp': index,
                        'stop_loss': stop_loss,
                        'take_profit': signal.take_profit,
                        'trailing_stop': None,
                        'best_price': entry_price
                    })
                    
                elif signal.signal in ['strong_sell', 'sell'] and self.position >= 0:
                    if self.position > 0:
                        # Close long position
                        pnl = (market_data['price'] - self.trades[-1]['price']) * self.position
                        self.capital += pnl
                        self.trade_history.append({
                            'entry_price': self.trades[-1]['price'],
                            'exit_price': market_data['price'],
                            'pnl': pnl,
                            'duration': index - self.trades[-1]['timestamp'],
                            'type': 'long'
                        })
                    
                    # Calculate new short position
                    risk_per_trade = self.capital * 0.02
                    if self.use_ai_stop:
                        stop_loss = self._ai_stop_loss(market_data, signal)
                    else:
                        stop_loss = signal.stop_loss if signal.stop_loss else market_data['price'] * 1.02
                    position_size = risk_per_trade / (stop_loss - market_data['price'])
                    
                    self.position = -position_size
                    self.trades.append({
                        'type': 'sell',
                        'price': market_data['price'],
                        'size': position_size,
                        'timestamp': index,
                        'stop_loss': stop_loss,
                        'take_profit': signal.take_profit,
                        'trailing_stop': None,
                        'best_price': market_data['price']
                    })
                
                # Trailing stop update (alleen als positie open is)
                if self.use_ai_stop and self.position != 0 and self.trades:
                    last_trade = self.trades[-1]
                    if last_trade['type'] == 'buy':
                        # Update best price
                        last_trade['best_price'] = max(last_trade.get('best_price', market_data['price']), market_data['price'])
                        last_trade['trailing_stop'] = self._ai_trailing_stop(
                            last_trade['price'], market_data['price'], last_trade['best_price'])
                    elif last_trade['type'] == 'sell':
                        last_trade['best_price'] = min(last_trade.get('best_price', market_data['price']), market_data['price'])
                        last_trade['trailing_stop'] = last_trade['best_price'] * (1 + 0.01)
                
                # Update equity curve
                current_value = self.capital
                if self.position > 0:
                    current_value += (market_data['price'] - self.trades[-1]['price']) * self.position
                elif self.position < 0:
                    current_value += (self.trades[-1]['price'] - market_data['price']) * abs(self.position)
                    
                results['equity_curve'].append({
                    'timestamp': index,
                    'equity': current_value
                })
            
            # Calculate performance metrics
            results['metrics'] = self._calculate_metrics(self.trade_history, results['equity_curve'])
            return results
            
        except Exception as e:
            logger.error(f"Error in backtest: {e}")
            return {'error': str(e)}
            
    def _calculate_metrics(self, trades: List[Dict], equity_curve: List[Dict]) -> Dict:
        """Calculate comprehensive trading metrics"""
        try:
            if not trades:
                return {}
                
            profits = [t['pnl'] for t in trades]
            win_trades = [p for p in profits if p > 0]
            loss_trades = [p for p in profits if p < 0]
            
            equity_values = [e['equity'] for e in equity_curve]
            returns = pd.Series(equity_values).pct_change().dropna()
            
            return {
                'total_trades': len(trades),
                'win_rate': len(win_trades) / len(trades) if trades else 0,
                'profit_factor': abs(sum(win_trades) / sum(loss_trades)) if loss_trades else float('inf'),
                'max_drawdown': self._calculate_max_drawdown(equity_values),
                'sharpe_ratio': returns.mean() / returns.std() * np.sqrt(252) if len(returns) > 0 else 0,
                'avg_trade_duration': sum((t['duration'] for t in trades), timedelta()) / len(trades) if trades else timedelta(),
                'total_return': (equity_values[-1] / self.initial_capital - 1) * 100 if equity_values else 0,
                'avg_win': sum(win_trades) / len(win_trades) if win_trades else 0,
                'avg_loss': sum(loss_trades) / len(loss_trades) if loss_trades else 0,
            }
            
        except Exception as e:
            logger.error(f"Error calculating metrics: {e}")
            return {}
            
    def _calculate_max_drawdown(self, equity_curve: List[float]) -> float:
        """Calculate maximum drawdown percentage"""
        try:
            peak = equity_curve[0]
            max_dd = 0
            
            for value in equity_curve:
                if value > peak:
                    peak = value
                dd = (peak - value) / peak
                max_dd = max(max_dd, dd)
                
            return max_dd * 100
            
        except Exception:
            return 0.0

if __name__ == "__main__":
    import asyncio
    import sys
    # Voorbeeld: laad historische data en run backtest met AI stops aan/uit
    # Laad een CSV met kolommen: ['timestamp','symbol','open','high','low','close','volume']
    if len(sys.argv) < 3:
        print("Gebruik: python backtesting.py <historical_data.csv> <strategy> [ai_stop:on|off]")
        sys.exit(1)
    
    csv_path = sys.argv[1]
    strategy_name = sys.argv[2]
    use_ai_stop = True
    if len(sys.argv) > 3 and sys.argv[3].lower() == 'off':
        use_ai_stop = False
    
    # Dynamisch strategie importeren
    from strategies.manager import get_strategy
    
    df = pd.read_csv(csv_path, parse_dates=['timestamp'])
    df.set_index('timestamp', inplace=True)
    
    strategy = get_strategy(strategy_name)()
    engine = BacktestEngine(initial_capital=10000, use_ai_stop=use_ai_stop)
    
    results = asyncio.run(engine.run_backtest(strategy, df))
    
    print("Backtest Resultaten:")
    print(results['metrics'])
    print(f"AI stop-loss {'AAN' if use_ai_stop else 'UIT'}")
    if 'equity_curve' in results:
        import matplotlib.pyplot as plt
        equity = [e['equity'] for e in results['equity_curve']]
        plt.plot(equity)
        plt.title('Equity Curve')
        plt.show()

    # Voorbeeld voor het instellen van een specifieke strategie met AI stops aan
    from strategies.day_trading import DayTradingStrategy
    config = {}  # Voeg hier je strategie-configuratie toe indien nodig
    strategy = DayTradingStrategy(config, use_ai_stop=True)  # AI stops aan