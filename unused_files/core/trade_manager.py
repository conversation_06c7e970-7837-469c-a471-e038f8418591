"""TradeManager: handles position sizing, order execution and position tracking."""
from loguru import logger
from typing import Dict, List, Optional, Tuple
from decimal import Decimal
from datetime import datetime
import asyncio
import uuid
import os
import ccxt

from config.settings import Settings
from core.storage import StorageManager
from core.position import Position

class TradeManager:
    def __init__(self):
        self.settings = Settings()
        self.storage = StorageManager()
        # initialise exchange (Binance by default)
        self.exchange = self._init_exchange()
        self.open_positions: Dict[str, Position] = {}
        self.trade_history: List[Dict] = []
        
        # Load risk settings from config
        self.max_open_positions = self.settings.MAX_OPEN_POSITIONS
        self.risk_per_trade = Decimal(str(self.settings.RISK_PER_TRADE))
        self.stop_loss_percent = Decimal(str(self.settings.STOP_LOSS_PERCENT))
        self.take_profit_percent = Decimal(str(self.settings.TAKE_PROFIT_PERCENT))
        self.position_sizing_method = self.settings.POSITION_SIZING_METHOD
        self.trailing_stop_enabled = self.settings.TRAILING_STOP_ENABLED
        self.trailing_stop_percent = Decimal(str(self.settings.TRAILING_STOP_PERCENT))
        self.position_scaling_enabled = self.settings.POSITION_SCALING_ENABLED
        self.scale_in_levels = [Decimal(str(x)) for x in self.settings.SCALE_IN_LEVELS]
        self.scale_out_levels = [Decimal(str(x)) for x in self.settings.SCALE_OUT_LEVELS]

    # ------------------------------------------------------------------ #
    def _init_exchange(self):
        """
        Returns a ccxt exchange instance.
        Uses Binance (spot) by default and supports testnet via settings.USE_TESTNET.
        """
        if self.settings.EXCHANGE.lower() == "kucoin":
            exchange = ccxt.kucoin({
                "apiKey": self.settings.KUCOIN_API_KEY,
                "secret": self.settings.KUCOIN_API_SECRET,
                "password": self.settings.KUCOIN_API_PASSPHRASE,
                "enableRateLimit": True,
            })
        else:  # default to Binance
            exchange = ccxt.binance({
                "apiKey": self.settings.BINANCE_API_KEY,
                "secret": self.settings.BINANCE_API_SECRET,
                "enableRateLimit": True,
                "options": {"defaultType": "spot"},
            })
            if getattr(self.settings, "USE_TESTNET", False):
                exchange.set_sandbox_mode(True)

        return exchange

    async def initialize(self):
        """Initialize trade manager"""
        logger.info("Initializing trade manager...")
        await self._load_open_positions()
        await self._load_trade_history()

    async def generate_signals(self, market_analysis: Dict, technical_analysis: Dict) -> List[Dict]:
        """Generate trade signals based on analysis"""
        try:
            signals = []
            symbol = market_analysis.get('symbol')
            current_price = Decimal(str(market_analysis.get('price', 0)))
            
            if not symbol or current_price == 0:
                return signals
            
            # Basic signal generation based on technical indicators
            if technical_analysis.get('score', 0) > 70:  # Strong buy signal
                # Calculate position size and price levels
                size, stop_loss, take_profit = await self._calculate_trade_levels(
                    symbol, 'buy', current_price, market_analysis.get('balance', Decimal('0'))
                )
                
                signals.append({
                    'symbol': symbol,
                    'side': 'buy',
                    'type': 'market',
                    'price': current_price,
                    'size': size,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'timestamp': datetime.utcnow().isoformat(),
                    'reason': 'Strong technical indicators'
                })
            elif technical_analysis.get('score', 0) < -70:  # Strong sell signal
                size, stop_loss, take_profit = await self._calculate_trade_levels(
                    symbol, 'sell', current_price, market_analysis.get('balance', Decimal('0'))
                )
                
                signals.append({
                    'symbol': symbol,
                    'side': 'sell',
                    'type': 'market',
                    'price': current_price,
                    'size': size,
                    'stop_loss': stop_loss,
                    'take_profit': take_profit,
                    'timestamp': datetime.utcnow().isoformat(),
                    'reason': 'Weak technical indicators'
                })
                
            return signals

        except Exception as e:
            logger.error(f"Error generating trade signals: {e}")
            return []

    async def execute_trade(self, signal: Dict) -> Dict:
        """Execute a trade based on the signal"""
        try:
            # Validate signal
            if not await self._validate_signal(signal):
                return {'status': 'error', 'message': 'Invalid signal'}

            # Check if we can open new position
            if len(self.open_positions) >= self.max_open_positions:
                return {'status': 'error', 'message': 'Max positions reached'}

            # Calculate position size
            position_size = await self._calculate_position_size(signal)
            if not position_size:
                return {'status': 'error', 'message': 'Invalid position size'}

            # Execute order
            order = await self._place_order(signal, position_size)
            if order['status'] == 'success':
                # Update positions
                await self._update_positions(signal, order)
                
            return order

        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return {'status': 'error', 'message': str(e)}

    async def update_positions(self):
        """Update all open positions"""
        try:
            for position_id, position in self.open_positions.items():
                await self._check_position_status(position_id)
                await self._update_position_pl(position_id)
                await self._check_exit_conditions(position_id)
        except Exception as e:
            logger.error(f"Error updating positions: {e}")

    async def close_position(self, position_id: str, reason: str) -> Dict:
        """Close a specific position"""
        try:
            position = self.open_positions.get(position_id)
            if not position:
                return {'status': 'error', 'message': 'Position not found'}

            # Execute closing order
            close_order = await self._place_order({
                'symbol': position['symbol'],
                'side': 'sell' if position['side'] == 'buy' else 'buy',
                'type': 'market'
            }, position['size'])

            if close_order['status'] == 'success':
                # Update position records
                await self._record_closed_position(position_id, close_order, reason)
                del self.open_positions[position_id]

            return close_order

        except Exception as e:
            logger.error(f"Error closing position: {e}")
            return {'status': 'error', 'message': str(e)}

    async def _validate_signal(self, signal: Dict) -> bool:
        """Validate trade signal"""
        required_fields = ['symbol', 'side', 'type']
        return all(field in signal for field in required_fields)

    async def _calculate_position_size(self, signal: Dict) -> Optional[Decimal]:
        """Calculate appropriate position size"""
        balance = Decimal(str(signal.get('balance', '0')))
        if balance == 0:
            try:
                balance_info = self.exchange.fetch_balance()
                balance = Decimal(str(balance_info['free'][signal['symbol'].split('/')[1]]))
            except Exception as e:
                logger.error(f"Unable to fetch balance, fallback 1000 USDT: {e}")
                balance = Decimal('1000')
        risk_amount = balance * self.risk_per_trade
        risk_per_unit = abs(signal['price'] - signal['stop_loss'])
        if risk_per_unit == 0:
            return None
        size = (risk_amount / risk_per_unit).quantize(Decimal('0.0001'))
        return size

    async def _place_order(self, signal: Dict, size: Decimal) -> Dict:
        """Place order with exchange"""
        try:
            if not getattr(self.settings, "LIVE_MODE", False):
                logger.info(f"[PAPER] {signal['side'].upper()} {signal['symbol']} size={size}")
                return {
                    "status": "success",
                    "order_id": f"paper-{uuid.uuid4()}",
                    "price": signal['price'],
                    "filled_size": size,
                    "timestamp": datetime.utcnow().timestamp()
                }

            order = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.exchange.create_market_order(signal['symbol'], signal['side'], float(size))
            )
            return {
                "status": "success",
                "order_id": order['id'],
                "price": Decimal(str(order.get('average') or order.get('price'))),
                "filled_size": Decimal(str(order['filled'])),
                "timestamp": order['timestamp'] / 1000
            }
        except Exception as e:
            logger.error(f"API order error: {e}")
            return {"status": "error", "message": str(e)}

    async def _update_positions(self, signal: Dict, order: Dict):
        """Update positions after new trade"""
        try:
            position_id = order['order_id']
            
            # Create new position with UUID
            position = Position(
                id=position_id,
                symbol=signal['symbol'],
                side=signal['side'],
                size=signal['size'],
                entry_price=signal['price'],
                current_price=signal['price'],
                timestamp=datetime.fromisoformat(signal['timestamp']),
                stop_loss=signal['stop_loss'],
                take_profit=signal['take_profit'],
                trailing_stop=signal['price'] * (1 - self.trailing_stop_percent) if self.trailing_stop_enabled else None,
                scale_levels=self._generate_scale_levels(signal) if self.position_scaling_enabled else []
            )
            
            # Add initial trade
            position.add_trade({
                'type': 'open',
                'price': signal['price'],
                'size': signal['size'],
                'order_id': order['order_id']
            })
            
            # Store position
            self.open_positions[position_id] = position
            await self.storage.save_position(position_id, position.to_dict())
            
        except Exception as e:
            logger.error(f"Error updating positions: {e}")

    async def _calculate_trade_levels(
        self, symbol: str, side: str, current_price: Decimal, balance: Decimal
    ) -> Tuple[Decimal, Decimal, Decimal]:
        """Calculate position size and stop loss/take profit levels"""
        try:
            # Calculate position size based on risk
            risk_amount = balance * self.risk_per_trade

            if side == 'buy':
                stop_loss_price = current_price * (1 - self.stop_loss_percent)
                take_profit_price = current_price * (1 + self.take_profit_percent)
            else:  # sell
                stop_loss_price = current_price * (1 + self.stop_loss_percent)
                take_profit_price = current_price * (1 - self.take_profit_percent)

            # Calculate size based on risk per trade
            risk_per_unit = abs(current_price - stop_loss_price)
            if risk_per_unit == 0:
                return Decimal('0'), stop_loss_price, take_profit_price

            position_size = (risk_amount / risk_per_unit).quantize(Decimal('0.00000001'))

            return position_size, stop_loss_price, take_profit_price

        except Exception as e:
            logger.error(f"Error calculating trade levels: {e}")
            return Decimal('0'), Decimal('0'), Decimal('0')

    async def _check_position_status(self, position_id: str):
        """Check and update position status"""
        try:
            position = self.open_positions.get(position_id)
            if not position:
                return

            # Get current market price
            current_price = await self._get_current_price(position['symbol'])
            if not current_price:
                return

            # Update position P&L
            await self._update_position_pl(position_id, current_price)

            # Check exit conditions
            await self._check_exit_conditions(position_id, current_price)

        except Exception as e:
            logger.error(f"Error checking position status: {e}")

    async def _update_position_pl(self, position_id: str, current_price: Decimal) -> Optional[Decimal]:
        """Update position profit/loss"""
        try:
            position = self.open_positions.get(position_id)
            if not position:
                return None

            entry_price = Decimal(str(position['entry_price']))
            size = Decimal(str(position['size']))

            if position['side'] == 'buy':
                pl = (current_price - entry_price) * size
            else:  # sell
                pl = (entry_price - current_price) * size

            position['current_pl'] = pl
            return pl

        except Exception as e:
            logger.error(f"Error updating position P&L: {e}")
            return None

    async def _check_exit_conditions(self, position_id: str, current_price: Decimal):
        """Check if position should be closed based on exit conditions"""
        try:
            position = self.open_positions.get(position_id)
            if not position:
                return

            stop_loss = Decimal(str(position['stop_loss']))
            take_profit = Decimal(str(position['take_profit']))

            # Check stop loss
            if position['side'] == 'buy' and current_price <= stop_loss:
                await self.close_position(position_id, 'Stop loss triggered')
            elif position['side'] == 'sell' and current_price >= stop_loss:
                await self.close_position(position_id, 'Stop loss triggered')

            # Check take profit
            elif position['side'] == 'buy' and current_price >= take_profit:
                await self.close_position(position_id, 'Take profit reached')
            elif position['side'] == 'sell' and current_price <= take_profit:
                await self.close_position(position_id, 'Take profit reached')

        except Exception as e:
            logger.error(f"Error checking exit conditions: {e}")

    async def _get_current_price(self, symbol: str) -> Optional[Decimal]:
        """Get current market price for symbol"""
        try:
            ticker = await asyncio.get_event_loop().run_in_executor(
                None, lambda: self.exchange.fetch_ticker(symbol)
            )
            return Decimal(str(ticker['last']))
        except Exception as e:
            logger.error(f"Error getting current price via API: {e}")
            return None

    async def _record_closed_position(self, position_id: str, close_order: Dict, reason: str):
        """Record closed position in trade history"""
        try:
            position = self.open_positions.get(position_id)
            if not position:
                return

            trade_data = {
                'trade_id': str(uuid.uuid4()),
                'position_id': position_id,
                'symbol': position['symbol'],
                'side': 'sell' if position['side'] == 'buy' else 'buy',
                'size': position['size'],
                'price': close_order['price'],
                'timestamp': datetime.utcnow().isoformat(),
                'pnl': position.get('current_pl', Decimal('0')),
                'reason': reason
            }

            # Record in storage
            self.storage.record_trade(trade_data)
            self.storage.close_position(position_id, trade_data)

        except Exception as e:
            logger.error(f"Error recording closed position: {e}")

    async def _load_open_positions(self):
        """Load open positions from storage"""
        try:
            self.open_positions = self.storage.load_open_positions()
        except Exception as e:
            logger.error(f"Error loading open positions: {e}")

    async def _load_trade_history(self):
        """Load trade history from storage"""
        try:
            self.trade_history = self.storage.load_trade_history()
        except Exception as e:
            logger.error(f"Error loading trade history: {e}")

    async def shutdown(self):
        """Graceful shutdown of trade manager"""
        try:
            # Close any open positions if needed
            for position_id in list(self.open_positions.keys()):
                await self.close_position(position_id, 'Bot shutdown')
        except Exception as e:
            logger.error(f"Error during trade manager shutdown: {e}")

    def _generate_scale_levels(self, signal: Dict) -> List[Dict]:
        """Generate scale in/out levels for position"""
        try:
            levels = []
            base_price = Decimal(str(signal['price']))
            
            if signal['side'] == 'buy':
                # Scale-in levels (lower than entry)
                for level in self.scale_in_levels:
                    levels.append({
                        'type': 'scale_in',
                        'price': base_price * (1 - level),
                        'size': Decimal(str(signal['size'])) * Decimal('0.5'),  # 50% of initial position
                        'executed': False
                    })
                
                # Scale-out levels (higher than entry)
                for level in self.scale_out_levels:
                    levels.append({
                        'type': 'scale_out',
                        'price': base_price * (1 + level),
                        'size': Decimal(str(signal['size'])) * Decimal('0.25'),  # 25% of initial position
                        'executed': False
                    })
            else:  # sell
                # Scale-in levels (higher than entry)
                for level in self.scale_in_levels:
                    levels.append({
                        'type': 'scale_in',
                        'price': base_price * (1 + level),
                        'size': Decimal(str(signal['size'])) * Decimal('0.5'),
                        'executed': False
                    })
                
                # Scale-out levels (lower than entry)
                for level in self.scale_out_levels:
                    levels.append({
                        'type': 'scale_out',
                        'price': base_price * (1 - level),
                        'size': Decimal(str(signal['size'])) * Decimal('0.25'),
                        'executed': False
                    })
            
            return levels
            
        except Exception as e:
            logger.error(f"Error generating scale levels: {e}")
            return []
