"""
Position tracking module
"""
from dataclasses import dataclass
from decimal import Decimal
from datetime import datetime
from typing import Optional, List, Dict
import uuid

@dataclass
class Position:
    id: str
    symbol: str
    side: str
    entry_price: Decimal
    current_price: Decimal
    size: Decimal
    timestamp: datetime
    stop_loss: Decimal
    take_profit: Decimal
    status: str = 'open'
    pnl: Decimal = Decimal('0')
    realized_pnl: Decimal = Decimal('0')
    unrealized_pnl: Decimal = Decimal('0')
    trades: List[Dict] = None
    trailing_stop: Optional[Decimal] = None
    scale_levels: List[Dict] = None

    def __init__(self, **kwargs):
        self.id = kwargs.get('id', str(uuid.uuid4()))
        self.symbol = kwargs['symbol']
        self.side = kwargs['side']
        self.entry_price = Decimal(str(kwargs['entry_price']))
        self.current_price = Decimal(str(kwargs['current_price']))
        self.size = Decimal(str(kwargs['size']))
        self.timestamp = kwargs.get('timestamp', datetime.utcnow())
        self.stop_loss = Decimal(str(kwargs['stop_loss']))
        self.take_profit = Decimal(str(kwargs['take_profit']))
        self.status = kwargs.get('status', 'open')
        self.pnl = Decimal('0')
        self.realized_pnl = Decimal('0')
        self.unrealized_pnl = Decimal('0')
        self.trades = kwargs.get('trades', [])
        self.trailing_stop = Decimal(str(kwargs['trailing_stop'])) if kwargs.get('trailing_stop') else None
        self.scale_levels = kwargs.get('scale_levels', [])

    def update_price(self, price: Decimal):
        """Update position with new price"""
        self.current_price = price
        self._calculate_pnl()
        self._update_trailing_stop()

    def _calculate_pnl(self):
        """Calculate current P&L"""
        if self.side == 'buy':
            self.unrealized_pnl = (self.current_price - self.entry_price) * self.size
        else:
            self.unrealized_pnl = (self.entry_price - self.current_price) * self.size
        
        self.pnl = self.realized_pnl + self.unrealized_pnl

    def _update_trailing_stop(self):
        """Update trailing stop if enabled"""
        if not self.trailing_stop:
            return

        if self.side == 'buy' and self.current_price > self.trailing_stop:
            # Update trailing stop to maintain the same percentage below current price
            trailing_distance = self.trailing_stop / self.entry_price
            self.trailing_stop = self.current_price * (1 - trailing_distance)
        elif self.side == 'sell' and self.current_price < self.trailing_stop:
            # Update trailing stop to maintain the same percentage above current price
            trailing_distance = self.trailing_stop / self.entry_price
            self.trailing_stop = self.current_price * (1 + trailing_distance)

    def add_trade(self, trade: Dict):
        """Add a trade to position history"""
        if self.trades is None:
            self.trades = []
        
        trade['timestamp'] = datetime.utcnow()
        trade['id'] = str(uuid.uuid4())
        self.trades.append(trade)

        # Update realized PnL if it's a closing trade
        if trade.get('type') == 'close':
            self.realized_pnl += Decimal(str(trade.get('pnl', '0')))

    def to_dict(self) -> Dict:
        """Convert position to dictionary for storage"""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'side': self.side,
            'entry_price': str(self.entry_price),
            'current_price': str(self.current_price),
            'size': str(self.size),
            'timestamp': self.timestamp.isoformat(),
            'stop_loss': str(self.stop_loss),
            'take_profit': str(self.take_profit),
            'status': self.status,
            'pnl': str(self.pnl),
            'realized_pnl': str(self.realized_pnl),
            'unrealized_pnl': str(self.unrealized_pnl),
            'trades': self.trades,
            'trailing_stop': str(self.trailing_stop) if self.trailing_stop else None,
            'scale_levels': self.scale_levels
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'Position':
        """Create position from dictionary"""
        if isinstance(data['timestamp'], str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)
