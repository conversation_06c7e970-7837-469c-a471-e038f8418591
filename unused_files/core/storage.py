"""
Storage utilities for trading data
"""
import json
import sqlite3
from pathlib import Path
from typing import Dict, List
from decimal import Decimal
from datetime import datetime

class StorageManager:
    def __init__(self, db_path: str = "trading_bot.db"):
        self.db_path = db_path
        self._init_db()

    def _init_db(self):
        """Initialize database tables"""
        conn = sqlite3.connect(self.db_path)
        cur = conn.cursor()

        # Create positions table
        cur.execute('''
            CREATE TABLE IF NOT EXISTS positions (
                position_id TEXT PRIMARY KEY,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                size TEXT NOT NULL,
                entry_price TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                take_profit TEXT,
                stop_loss TEXT,
                status TEXT DEFAULT 'open'
            )
        ''')

        # Create trade history table
        cur.execute('''
            CREATE TABLE IF NOT EXISTS trade_history (
                trade_id TEXT PRIMARY KEY,
                position_id TEXT,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                size TEXT NOT NULL,
                price TEXT NOT NULL,
                timestamp TEXT NOT NULL,
                pnl TEXT,
                reason TEXT
            )
        ''')

        conn.commit()
        conn.close()

    def load_open_positions(self) -> Dict[str, Dict]:
        """Load open positions from database"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cur = conn.cursor()
        
        cur.execute("SELECT * FROM positions WHERE status = 'open'")
        rows = cur.fetchall()
        
        positions = {}
        for row in rows:
            positions[row['position_id']] = {
                'symbol': row['symbol'],
                'side': row['side'],
                'size': Decimal(row['size']),
                'entry_price': Decimal(row['entry_price']),
                'timestamp': row['timestamp'],
                'take_profit': Decimal(row['take_profit']) if row['take_profit'] else None,
                'stop_loss': Decimal(row['stop_loss']) if row['stop_loss'] else None
            }
        
        conn.close()
        return positions

    def load_trade_history(self) -> List[Dict]:
        """Load trade history from database"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cur = conn.cursor()
        
        cur.execute("SELECT * FROM trade_history ORDER BY timestamp DESC")
        rows = cur.fetchall()
        
        history = []
        for row in rows:
            history.append({
                'trade_id': row['trade_id'],
                'position_id': row['position_id'],
                'symbol': row['symbol'],
                'side': row['side'],
                'size': Decimal(row['size']),
                'price': Decimal(row['price']),
                'timestamp': row['timestamp'],
                'pnl': Decimal(row['pnl']) if row['pnl'] else None,
                'reason': row['reason']
            })
        
        conn.close()
        return history

    def save_position(self, position_id: str, position_data: Dict):
        """Save or update position in database"""
        conn = sqlite3.connect(self.db_path)
        cur = conn.cursor()
        
        cur.execute('''
            INSERT OR REPLACE INTO positions 
            (position_id, symbol, side, size, entry_price, timestamp, take_profit, stop_loss, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            position_id,
            position_data['symbol'],
            position_data['side'],
            str(position_data['size']),
            str(position_data['entry_price']),
            position_data['timestamp'],
            str(position_data.get('take_profit', '')),
            str(position_data.get('stop_loss', '')),
            position_data.get('status', 'open')
        ))
        
        conn.commit()
        conn.close()

    def record_trade(self, trade_data: Dict):
        """Record trade in history"""
        conn = sqlite3.connect(self.db_path)
        cur = conn.cursor()
        
        cur.execute('''
            INSERT INTO trade_history 
            (trade_id, position_id, symbol, side, size, price, timestamp, pnl, reason)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            trade_data['trade_id'],
            trade_data.get('position_id', ''),
            trade_data['symbol'],
            trade_data['side'],
            str(trade_data['size']),
            str(trade_data['price']),
            trade_data['timestamp'],
            str(trade_data.get('pnl', '')),
            trade_data.get('reason', '')
        ))
        
        conn.commit()
        conn.close()

    def close_position(self, position_id: str, close_data: Dict):
        """Mark position as closed and record final trade"""
        conn = sqlite3.connect(self.db_path)
        cur = conn.cursor()
        
        # Update position status
        cur.execute('''
            UPDATE positions 
            SET status = 'closed' 
            WHERE position_id = ?
        ''', (position_id,))
        
        # Record closing trade
        self.record_trade({
            'trade_id': close_data['trade_id'],
            'position_id': position_id,
            'symbol': close_data['symbol'],
            'side': close_data['side'],
            'size': str(close_data['size']),
            'price': str(close_data['price']),
            'timestamp': close_data['timestamp'],
            'pnl': str(close_data.get('pnl', '')),
            'reason': close_data.get('reason', 'Position closed')
        })
        
        conn.commit()
        conn.close()
