"""
Notification Module for Trading Bot
"""
import asyncio
from loguru import logger
from typing import Dict, Optional, Union
import telegram
from telegram.error import TelegramError
import os
from datetime import datetime

class Notifier:
    def __init__(self):
        self.bot_token = os.getenv('TELEGRAM_BOT_TOKEN')
        self.admin_ids = [int(id) for id in os.getenv('TELEGRAM_ADMIN_USER_ID', '').split(',') if id]
        self.bot = None
        self.notification_queue = asyncio.Queue()
        self.is_running = False

    async def initialize(self):
        """Initialize the notifier"""
        try:
            logger.info("Initializing notifier...")
            if self.bot_token:
                self.bot = telegram.Bot(token=self.bot_token)
                self.is_running = True
                asyncio.create_task(self._process_notification_queue())
                await self.send_message("🤖 Trading Bot Started")
            else:
                logger.warning("Telegram bot token not configured")
        except Exception as e:
            logger.error(f"Error initializing notifier: {e}")

    async def send_trade_notification(self, trade_info: Dict):
        """Send notification about trade execution"""
        try:
            emoji = "🟢" if trade_info.get('side') == 'buy' else "🔴"
            message = (
                f"{emoji} Trade Executed\n"
                f"Symbol: {trade_info.get('symbol')}\n"
                f"Side: {trade_info.get('side', '').upper()}\n"
                f"Price: {trade_info.get('price')}\n"
                f"Size: {trade_info.get('size')}\n"
                f"Status: {trade_info.get('status')}"
            )
            await self.send_message(message)
        except Exception as e:
            logger.error(f"Error sending trade notification: {e}")

    async def send_error_notification(self, error_info: Dict):
        """Send notification about errors"""
        try:
            message = (
                "⚠️ Error Alert\n"
                f"Type: {error_info.get('type')}\n"
                f"Message: {error_info.get('message')}\n"
                f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            await self.send_message(message, priority='high')
        except Exception as e:
            logger.error(f"Error sending error notification: {e}")

    async def send_alert(self, alert_info: Dict):
        """Send general trading alerts"""
        try:
            message = (
                "🔔 Trading Alert\n"
                f"Type: {alert_info.get('type')}\n"
                f"Message: {alert_info.get('message')}\n"
                f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            await self.send_message(message)
        except Exception as e:
            logger.error(f"Error sending alert: {e}")

    async def send_performance_update(self, performance_info: Dict):
        """Send periodic performance updates"""
        try:
            message = (
                "📊 Performance Update\n"
                f"Period: {performance_info.get('period')}\n"
                f"PnL: {performance_info.get('pnl')}%\n"
                f"Win Rate: {performance_info.get('win_rate')}%\n"
                f"Total Trades: {performance_info.get('total_trades')}"
            )
            await self.send_message(message)
        except Exception as e:
            logger.error(f"Error sending performance update: {e}")

    async def send_message(self, message: str, priority: str = 'normal'):
        """Add message to notification queue"""
        try:
            await self.notification_queue.put({
                'message': message,
                'priority': priority,
                'timestamp': datetime.now()
            })
        except Exception as e:
            logger.error(f"Error queueing message: {e}")

    async def _process_notification_queue(self):
        """Process the notification queue"""
        while self.is_running:
            try:
                notification = await self.notification_queue.get()
                if self.bot and self.admin_ids:
                    for admin_id in self.admin_ids:
                        try:
                            await self.bot.send_message(
                                chat_id=admin_id,
                                text=notification['message'],
                                parse_mode='HTML'
                            )
                            # Add delay to prevent hitting rate limits
                            await asyncio.sleep(0.1)
                        except TelegramError as te:
                            logger.error(f"Telegram error for user {admin_id}: {te}")
                        except Exception as e:
                            logger.error(f"Error sending message to {admin_id}: {e}")
                
                self.notification_queue.task_done()
            except Exception as e:
                logger.error(f"Error processing notification queue: {e}")
                await asyncio.sleep(1)

    async def shutdown(self):
        """Graceful shutdown of notifier"""
        try:
            logger.info("Shutting down notifier...")
            self.is_running = False
            
            # Process remaining notifications
            while not self.notification_queue.empty():
                await asyncio.sleep(0.1)
                
            if self.bot:
                await self.send_message("🔄 Trading Bot Shutting Down")
                
        except Exception as e:
            logger.error(f"Error during notifier shutdown: {e}")

    def format_trade_message(self, trade_info: Dict) -> str:
        """Format trade information into a readable message"""
        try:
            return (
                f"{'🟢' if trade_info['side'] == 'buy' else '🔴'} Trade Details\n"
                f"Symbol: {trade_info['symbol']}\n"
                f"Side: {trade_info['side'].upper()}\n"
                f"Entry: {trade_info.get('entry_price', 'N/A')}\n"
                f"Size: {trade_info.get('size', 'N/A')}\n"
                f"Type: {trade_info.get('type', 'N/A')}\n"
                f"Time: {datetime.now().strftime('%H:%M:%S')}"
            )
        except Exception as e:
            logger.error(f"Error formatting trade message: {e}")
            return "Error formatting trade message"
