"""
Main entry point for the Telegram Trading Bot
"""
import asyncio
import sys
from telegram.ext import Application, CommandHandler, CallbackQueryHandler
from loguru import logger
from config import get_settings
from exchanges.manager import ExchangeManager
from bot.handlers import TradingBotHandlers
from bot.callbacks import CallbackHandlers
from strategies.manager import StrategyManager
from analysis.market_analyzer import MarketAnalyzer

async def main():
    """Main function to start the bot"""

    # Setup logging
    settings = get_settings()
    logger.remove()
    logger.add(
        sys.stderr,
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    logger.add(
        settings.log_file,
        level=settings.log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB"
    )

    logger.info("Starting Telegram Trading Bot...")

    try:
        # Initialize exchange manager
        exchange_manager = ExchangeManager()

        # Connect to exchanges
        logger.info("Connecting to exchanges...")
        connection_results = await exchange_manager.connect_all()

        for exchange, connected in connection_results.items():
            if connected:
                logger.info(f"✅ Connected to {exchange}")
            else:
                logger.error(f"❌ Failed to connect to {exchange}")

        # Check if at least one exchange is connected
        if not any(connection_results.values()):
            logger.error("No exchanges connected. Exiting...")
            return

        # Initialize strategy manager
        strategy_manager = StrategyManager(exchange_manager)

        # Initialize market analyzer
        market_analyzer = MarketAnalyzer(exchange_manager)

        # Initialize handlers
        handlers = TradingBotHandlers(exchange_manager)
        handlers.strategy_manager = strategy_manager
        handlers.market_analyzer = market_analyzer

        callback_handlers = CallbackHandlers(exchange_manager)
        callback_handlers.strategy_manager = strategy_manager

        # Create application with timezone
        import pytz
        application = Application.builder().token(settings.telegram_bot_token).build()

        # Add command handlers
        application.add_handler(CommandHandler("start", handlers.start))
        application.add_handler(CommandHandler("help", handlers.help_command))
        application.add_handler(CommandHandler("balance", handlers.balance))
        application.add_handler(CommandHandler("price", handlers.price))
        application.add_handler(CommandHandler("buy", handlers.buy))
        application.add_handler(CommandHandler("sell", handlers.sell))
        application.add_handler(CommandHandler("orders", handlers.orders))
        application.add_handler(CommandHandler("cancel", handlers.cancel))
        application.add_handler(CommandHandler("exchanges", handlers.exchanges))
        application.add_handler(CommandHandler("bestprice", handlers.best_price))

        # New advanced commands
        application.add_handler(CommandHandler("analysis", handlers.market_analysis))
        application.add_handler(CommandHandler("strategies", handlers.strategies))
        application.add_handler(CommandHandler("positions", handlers.positions))
        application.add_handler(CommandHandler("starttrading", handlers.start_auto_trading))
        application.add_handler(CommandHandler("stoptrading", handlers.stop_auto_trading))
        application.add_handler(CommandHandler("daytrade", handlers.daytrade))

        # Add callback handler
        application.add_handler(CallbackQueryHandler(callback_handlers.button_callback))

        logger.info("Bot handlers registered")

        # Start market analyzer in background
        logger.info("Starting market analyzer...")
        market_analysis_task = asyncio.create_task(market_analyzer.start_analysis())

        # Start the bot
        logger.info("Starting bot polling...")

        try:
            # Use the new run_polling method for version 22.x
            await application.run_polling(allowed_updates=["message", "callback_query"])

        except Exception as e:
            logger.error(f"Error during bot operation: {e}")
        finally:
            # Cleanup
            logger.info("Shutting down bot...")
            market_analyzer.stop_analysis()
            strategy_manager.stop_automated_trading()

            try:
                await market_analysis_task
            except Exception as e:
                logger.error(f"Error during shutdown: {e}")

    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Error starting bot: {e}")
        raise

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot shutdown complete")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
