#!/usr/bin/env python3
"""
Test script om de configuratie te controleren
"""
import sys
import os

# Voeg de huidige directory toe aan het Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import directly from the root config.py file
sys.path.insert(0, '.')
import importlib.util
spec = importlib.util.spec_from_file_location("root_config", "./config.py")
root_config = importlib.util.module_from_spec(spec)
spec.loader.exec_module(root_config)
get_settings = root_config.get_settings

def test_configuration():
    """Test alle configuratie instellingen"""
    print("🔍 Configuratie Test")
    print("=" * 50)

    try:
        settings = get_settings()

        # Test basis configuratie
        print("📋 Basis Configuratie:")
        print(f"  ✅ Telegram Bot Token: {'✓' if settings.telegram_bot_token else '❌'}")
        print(f"  ✅ Admin User IDs: {settings.telegram_admin_user_ids}")
        print(f"  ✅ Backward compatibility ID: {settings.telegram_admin_user_id}")

        # Test autorisatie
        print("\n🔐 Autorisatie Test:")
        test_user_1 = 6229184945
        test_user_2 = 7926586899
        test_user_3 = 12345  # Ongeautoriseerde gebruiker

        print(f"  User {test_user_1}: {'✅ Geautoriseerd' if settings.is_authorized_user(test_user_1) else '❌ Niet geautoriseerd'}")
        print(f"  User {test_user_2}: {'✅ Geautoriseerd' if settings.is_authorized_user(test_user_2) else '❌ Niet geautoriseerd'}")
        print(f"  User {test_user_3}: {'✅ Geautoriseerd' if settings.is_authorized_user(test_user_3) else '❌ Niet geautoriseerd (correct)'}")

        # Test exchange configuratie
        print("\n🏦 Exchange Configuratie:")
        print(f"  KuCoin API Key: {'✅' if settings.kucoin_api_key else '❌'}")
        print(f"  KuCoin Secret: {'✅' if settings.kucoin_secret_key else '❌'}")
        print(f"  KuCoin Passphrase: {'✅' if settings.kucoin_passphrase else '❌'}")
        print(f"  KuCoin Sandbox: {settings.kucoin_sandbox}")

        print(f"  MEXC API Key: {'✅' if settings.mexc_api_key else '❌'}")
        print(f"  MEXC Secret: {'✅' if settings.mexc_secret_key else '❌'}")
        print(f"  MEXC Sandbox: {settings.mexc_sandbox}")

        # Test database en logging
        print("\n💾 Database & Logging:")
        print(f"  Database URL: {settings.database_url}")
        print(f"  Log Level: {settings.log_level}")
        print(f"  Log File: {settings.log_file}")

        # Test validatie
        print("\n✅ Validatie Test:")
        is_valid = settings.validate()
        print(f"  Configuratie geldig: {'✅ Ja' if is_valid else '❌ Nee'}")

        return is_valid

    except Exception as e:
        print(f"❌ Fout bij het testen van configuratie: {e}")
        return False

if __name__ == "__main__":
    success = test_configuration()
    if success:
        print("\n🎉 Alle configuratie tests geslaagd!")
        sys.exit(0)
    else:
        print("\n💥 Er zijn problemen met de configuratie!")
        sys.exit(1)
