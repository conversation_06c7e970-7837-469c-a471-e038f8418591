#!/usr/bin/env python3
"""
Eenvoudige startup script voor de Trading Bot
"""
import sys
import subprocess
import os

def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'ccxt',
        'loguru', 
        'python-dotenv',
        'aiohttp'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing.append(package)
            print(f"❌ {package} - MISSING")
    
    return missing

def install_missing(packages):
    """Install missing packages"""
    if not packages:
        return True
    
    print(f"\n📦 Installing missing packages: {', '.join(packages)}")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + packages)
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def check_config():
    """Check if .env file exists and has required settings"""
    env_file = '.env'
    
    if not os.path.exists(env_file):
        print("❌ .env file not found!")
        return False
    
    required_vars = [
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_ADMIN_USER_ID',
        'KUCOIN_API_KEY',
        'MEXC_API_KEY'
    ]
    
    with open(env_file, 'r') as f:
        content = f.read()
    
    missing_vars = []
    for var in required_vars:
        if f"{var}=" not in content or f"{var}=your_" in content or f"{var}=test" in content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing or invalid config vars: {', '.join(missing_vars)}")
        return False
    
    print("✅ Configuration file OK")
    return True

def start_console_bot():
    """Start the console bot"""
    print("\n🚀 Starting Console Trading Bot...")
    print("=" * 50)
    
    try:
        # Import and run the simple bot
        from simple_bot import SimpleTradingBot
        import asyncio
        
        async def run_bot():
            bot = SimpleTradingBot()
            
            if not await bot.initialize():
                print("❌ Failed to initialize bot")
                return
            
            print("✅ Bot initialized successfully!")
            print("\n🎮 Console Interface Started")
            print("Type commands like: balance, price BTC/USDT, analysis, start, quit")
            
            await bot.run_console_interface()
        
        asyncio.run(run_bot())
        
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        print(f"❌ Error starting bot: {e}")
        print("\n💡 Try running: python simple_bot.py")

def start_telegram_bot():
    """Start the Telegram bot"""
    print("\n🚀 Starting Telegram Trading Bot...")
    print("=" * 50)
    
    try:
        # Import and run the telegram bot
        from telegram_simple import SimpleTelegramBot
        import asyncio
        
        async def run_bot():
            bot = SimpleTelegramBot()
            await bot.run()
        
        asyncio.run(run_bot())
        
    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        print(f"❌ Error starting Telegram bot: {e}")
        print("\n💡 Try the console bot instead: python start_bot.py --console")

def main():
    """Main startup function"""
    print("🤖 Trading Bot Startup")
    print("=" * 30)
    
    # Check dependencies
    print("\n📦 Checking dependencies...")
    missing = check_dependencies()
    
    if missing:
        if not install_missing(missing):
            print("\n❌ Failed to install dependencies. Please install manually:")
            print(f"pip install {' '.join(missing)}")
            return
    
    # Check configuration
    print("\n⚙️ Checking configuration...")
    if not check_config():
        print("\n❌ Configuration issues found. Please check your .env file.")
        return
    
    # Choose bot type
    if len(sys.argv) > 1 and sys.argv[1] == '--console':
        start_console_bot()
    elif len(sys.argv) > 1 and sys.argv[1] == '--telegram':
        start_telegram_bot()
    else:
        print("\n🎯 Choose bot type:")
        print("1. Console Bot (Recommended for testing)")
        print("2. Telegram Bot (Full interface)")
        
        choice = input("\nEnter choice (1 or 2): ").strip()
        
        if choice == "1":
            start_console_bot()
        elif choice == "2":
            start_telegram_bot()
        else:
            print("❌ Invalid choice. Starting console bot...")
            start_console_bot()

if __name__ == "__main__":
    main()
