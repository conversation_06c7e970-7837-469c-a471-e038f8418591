# 🚀 Trading Bot Start Guide

## 📋 Voordat je begint

✅ **Alles is al geconfigureerd en getest!**
- Configuratie: ✅ Correct
- API Keys: ✅ Ingesteld  
- Exchanges: ✅ KuCoin & MEXC verbonden
- Autorisatie: ✅ Beide users (6229184945, 7926586899)
- Dependencies: ✅ Geïnstalleerd

## 🎯 Start Opties

### **Optie 1: Eenvoudige Start (AANBEVOLEN)**
```bash
python simple_telegram_bot.py
```
- Interactief menu
- Automatische tests
- Foutafhandeling
- Keuze tussen Telegram of Console bot

### **Optie 2: Direct Telegram Bot**
```bash
python telegram_simple.py
```
- Start direct de Telegram bot
- Volledig functioneel
- Alle trading functies beschikbaar

### **Optie 3: Volledige Bot (Geavanceerd)**
```bash
python main.py
```
- Alle geavanceerde functies
- Market analyzer
- Automatische trading strategieën
- AI-driven analysis

### **Optie 4: Start Script (Mac/Linux)**
```bash
chmod +x start.sh
./start.sh
```
- Automatische dependency check
- Interactief menu
- Platform specifiek

## 📱 Telegram Bot Gebruik

### **Basis Commando's:**
- `/start` - Start de bot
- `/help` - Toon alle commando's
- `/balance` - Bekijk balansen
- `/price BTC/USDT` - Krijg prijzen
- `/exchanges` - Bekijk exchange status

### **Trading Commando's:**
- `/buy` - Koop orders
- `/sell` - Verkoop orders
- `/orders` - Actieve orders
- `/positions` - Open posities

### **Geavanceerde Functies:**
- `/analysis` - Market analyse
- `/strategies` - Trading strategieën
- `/starttrading` - Start automatisch handelen
- `/daytrade` - Daytrading modus

## 🔧 Troubleshooting

### **Als de bot niet start:**
1. **Check dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Test configuratie:**
   ```bash
   python test_config.py
   ```

3. **Test exchanges:**
   ```bash
   python test_exchanges_quick.py
   ```

4. **Debug modus:**
   ```bash
   python debug_bot.py
   ```

### **Veelvoorkomende problemen:**

**❌ "ImportError"**
```bash
pip install --upgrade pip
pip install -r requirements.txt
```

**❌ "Telegram token invalid"**
- Check .env file
- Verifieer TELEGRAM_BOT_TOKEN

**❌ "Exchange connection failed"**
- Check API keys in .env
- Verifieer internet connectie

**❌ "Unauthorized user"**
- Check TELEGRAM_ADMIN_USER_ID in .env
- Moet zijn: `6229184945,7926586899`

## 🎮 Eerste Gebruik

### **Stap 1: Start de bot**
```bash
python simple_telegram_bot.py
```

### **Stap 2: Kies optie 1 (Telegram Bot)**

### **Stap 3: Open Telegram**
- Zoek je bot (@jouw_bot_naam)
- Type `/start`
- Volg de instructies

### **Stap 4: Test basis functies**
- `/balance` - Check je balansen
- `/price BTC/USDT` - Test prijsdata
- `/exchanges` - Verifieer connecties

## 🛡️ Veiligheid

- **Nooit** je API keys delen
- **Altijd** kleine bedragen gebruiken voor tests
- **Controleer** orders voordat je bevestigt
- **Stop** de bot als er problemen zijn

## 📞 Hulp Nodig?

Als je problemen hebt:
1. Run `python test_config.py` voor diagnostics
2. Check de logs in `trading_bot.log`
3. Gebruik debug modus: `python debug_bot.py`

**De bot is klaar voor gebruik! 🎉**
