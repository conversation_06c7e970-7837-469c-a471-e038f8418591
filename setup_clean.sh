#!/bin/bash
# Setup script voor MyOwnMoneyMaker Trading Bot
# Dit script fixt alle dependency problemen

echo "🔧 MyOwnMoneyMaker Trading Bot Setup"
echo "====================================="
echo ""

# Check Python version
PYTHON_VERSION=$(python3 --version 2>&1 | awk '{print $2}')
echo "📊 Python versie: $PYTHON_VERSION"

# Stop any running bot
echo "🛑 Stopping any running bot instances..."
./stop-24-7.sh 2>/dev/null || true

# Clean up old virtual environment
echo "🗑️  Cleaning up old environment..."
rm -rf venv

# Create new virtual environment with system python
echo "🏗️  Creating new virtual environment..."
python3 -m venv venv --system-site-packages

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "📦 Upgrading pip..."
pip install --upgrade pip setuptools wheel

# Install basic requirements first
echo "📋 Installing basic requirements..."
pip install python-dotenv loguru aiohttp

# Install telegram bot with specific httpx version
echo "📱 Installing Telegram bot..."
pip install httpx==0.26.0
pip install python-telegram-bot==20.8

# Install exchange libraries
echo "💱 Installing exchange libraries..."
pip install ccxt==4.4.76

# Install data analysis
echo "📊 Installing data analysis libraries..."
pip install pandas pandas_ta

# Install other dependencies
echo "🔧 Installing other dependencies..."
pip install cryptography tweepy praw newsapi-python

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs data

# Set permissions
echo "🔐 Setting permissions..."
chmod +x start-24-7.sh stop-24-7.sh restart-24-7.sh status-24-7.sh monitor-24-7.sh

# Test imports
echo "🧪 Testing imports..."
python3 -c "
import sys
print('✅ Python:', sys.version.split()[0])
try:
    import telegram
    print('✅ Telegram bot module loaded')
except ImportError as e:
    print('❌ Telegram bot import failed:', e)

try:
    import ccxt
    print('✅ CCXT module loaded')
except ImportError as e:
    print('❌ CCXT import failed:', e)

try:
    import pandas
    print('✅ Pandas module loaded')
except ImportError as e:
    print('❌ Pandas import failed:', e)

try:
    from exchanges.manager import ExchangeManager
    print('✅ Exchange manager loaded')
except ImportError as e:
    print('⚠️  Exchange manager import failed (expected):', e)
"

echo ""
echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Activate the virtual environment: source venv/bin/activate"
echo "2. Check configuration: python3 check_config.py"
echo "3. Start the bot: ./start-24-7.sh"
echo ""
echo "⚠️  IMPORTANT: Your bot is now in LIVE mode (not sandbox)!"
echo "   Start with VERY small amounts to test!"
