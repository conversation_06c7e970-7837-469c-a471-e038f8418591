"""
Minimal test to isolate the timezone issue
"""
import asyncio
import os
import pytz

# Set timezone environment variable before importing telegram
os.environ['TZ'] = 'UTC'

async def test_imports():
    """Test imports one by one"""
    print("🧪 Testing imports...")

    try:
        print("  Testing telegram import...")
        from telegram.ext import Application
        print("  ✅ telegram.ext imported")

        print("  Testing pytz import...")
        import pytz
        print("  ✅ pytz imported")

        print("  Testing config import...")
        from config import get_settings
        settings = get_settings()
        print("  ✅ config imported")

        print("  Testing Application creation...")
        # Try to create application with minimal settings
        if settings.telegram_bot_token:
            # Try creating application with timezone already set
            app = Application.builder().token(settings.telegram_bot_token).build()
            print("  ✅ Application created")
        else:
            print("  ❌ No bot token")

        return True

    except Exception as e:
        print(f"  ❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Main test"""
    print("🔍 Minimal Telegram Test")
    print("=" * 30)

    await test_imports()

if __name__ == "__main__":
    asyncio.run(main())
