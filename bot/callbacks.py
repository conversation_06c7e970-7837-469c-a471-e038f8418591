"""
Telegram bot callback handlers
"""
from decimal import Decimal
from telegram import Update
from telegram.ext import ContextTypes
from exchanges.manager import ExchangeManager
from loguru import logger

class CallbackHandlers:
    """Telegram bot callback handlers"""

    def __init__(self, exchange_manager: ExchangeManager):
        self.exchange_manager = exchange_manager

    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle button callbacks"""
        query = update.callback_query
        await query.answer()

        data = query.data

        if data == "cancel_order":
            await query.edit_message_text("❌ Order geannuleerd.")
            return

        if data.startswith("confirm_buy_"):
            await self._handle_buy_confirmation(query, data)
        elif data.startswith("confirm_sell_"):
            await self._handle_sell_confirmation(query, data)
        elif data.startswith("start_daytrade_"):
            await self._handle_daytrade_start(query, data)

    async def _handle_buy_confirmation(self, query, data: str):
        """Handle buy order confirmation"""
        try:
            # Parse callback data: confirm_buy_exchange_symbol_amount_price
            parts = data.split("_", 4)
            exchange_name = parts[2]
            symbol = parts[3]
            amount = Decimal(parts[4])
            price_str = parts[5] if len(parts) > 5 else "market"

            price = None if price_str == "market" else Decimal(price_str)
            order_type = "limit" if price else "market"

            await query.edit_message_text("🔄 Order wordt geplaatst...")

            # Create the order
            order = await self.exchange_manager.create_order(
                exchange_name=exchange_name,
                order_type=order_type,
                side="buy",
                symbol=symbol,
                amount=amount,
                price=price
            )

            # Success message
            success_msg = f"✅ **Buy Order Geplaatst!**\n\n"
            success_msg += f"Order ID: `{order.id}`\n"
            success_msg += f"Exchange: {exchange_name.upper()}\n"
            success_msg += f"Symbol: {symbol}\n"
            success_msg += f"Type: {order_type.upper()}\n"
            success_msg += f"Amount: {amount}\n"
            if price:
                success_msg += f"Price: ${price}\n"
            success_msg += f"Status: {order.status}\n"

            await query.edit_message_text(success_msg, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Error creating buy order: {e}")
            await query.edit_message_text(f"❌ Fout bij plaatsen order: {str(e)}")

    async def _handle_sell_confirmation(self, query, data: str):
        """Handle sell order confirmation"""
        try:
            # Parse callback data: confirm_sell_exchange_symbol_amount_price
            parts = data.split("_", 4)
            exchange_name = parts[2]
            symbol = parts[3]
            amount = Decimal(parts[4])
            price_str = parts[5] if len(parts) > 5 else "market"

            price = None if price_str == "market" else Decimal(price_str)
            order_type = "limit" if price else "market"

            await query.edit_message_text("🔄 Order wordt geplaatst...")

            # Create the order
            order = await self.exchange_manager.create_order(
                exchange_name=exchange_name,
                order_type=order_type,
                side="sell",
                symbol=symbol,
                amount=amount,
                price=price
            )

            # Success message
            success_msg = f"✅ **Sell Order Geplaatst!**\n\n"
            success_msg += f"Order ID: `{order.id}`\n"
            success_msg += f"Exchange: {exchange_name.upper()}\n"
            success_msg += f"Symbol: {symbol}\n"
            success_msg += f"Type: {order_type.upper()}\n"
            success_msg += f"Amount: {amount}\n"
            if price:
                success_msg += f"Price: ${price}\n"
            success_msg += f"Status: {order.status}\n"

            await query.edit_message_text(success_msg, parse_mode='Markdown')

        except Exception as e:
            logger.error(f"Error creating sell order: {e}")
            await query.edit_message_text(f"❌ Fout bij plaatsen order: {str(e)}")

    async def _handle_daytrade_start(self, query, data: str):
        """Handle day trading start confirmation"""
        try:
            # Parse callback data: start_daytrade_amount
            parts = data.split("_")
            amount = Decimal(parts[2])

            await query.edit_message_text("🤖 Day trading wordt gestart...")

            # Enable day trading strategy with specified amount
            if hasattr(self.exchange_manager, 'strategy_manager'):
                strategy_manager = self.exchange_manager.strategy_manager

                # Enable day trading strategy
                strategy_manager.enable_strategy('daytrading')

                # Start automated trading
                import asyncio
                asyncio.create_task(strategy_manager.start_automated_trading())

                success_msg = f"✅ **Day Trading Gestart!**\n\n"
                success_msg += f"💰 Bedrag: ${amount}\n"
                success_msg += f"🤖 AI Day Trading: Actief\n"
                success_msg += f"🛡️ Auto Stop-Loss: Actief\n"
                success_msg += f"📈 Trailing Stop: Actief\n\n"
                success_msg += f"📊 Gebruik /positions om posities te bekijken\n"
                success_msg += f"🛑 Gebruik /stoptrading om te stoppen"

                await query.edit_message_text(success_msg, parse_mode='Markdown')
            else:
                await query.edit_message_text("❌ Strategy manager niet beschikbaar")

        except Exception as e:
            logger.error(f"Error starting day trading: {e}")
            await query.edit_message_text(f"❌ Fout bij starten day trading: {str(e)}")
