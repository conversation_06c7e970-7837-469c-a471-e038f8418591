#!/bin/bash

# 24/7 Trading Bot Startup Script (without <PERSON><PERSON>)
echo "🚀 Starting MyOwnMoneyMaker Trading Bot 24/7..."

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 is not installed. Please install Python3 first."
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create .env file with your API keys."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs
mkdir -p data

# Install dependencies if needed
echo "📦 Checking dependencies..."
python3 -m pip install -r requirements.txt --quiet

# Stop any existing process
echo "🛑 Stopping existing bot processes..."
pkill -f "python.*telegram_simple.py" 2>/dev/null || true

# Start the bot in background with nohup
echo "🚀 Starting the trading bot in background..."
nohup python3 telegram_simple.py > logs/bot.log 2>&1 &
BOT_PID=$!

# Wait a moment to check if it started successfully
sleep 5

if ps -p $BOT_PID > /dev/null; then
    echo "✅ Trading bot is now running 24/7!"
    echo "📋 Process ID: $BOT_PID"
    echo "📝 Log file: logs/bot.log"
    echo ""
    echo "📊 Bot status:"
    ps aux | grep "python.*telegram_simple.py" | grep -v grep
    echo ""
    echo "📝 Recent logs:"
    tail -20 logs/bot.log
    echo ""
    echo "Useful commands:"
    echo "  View logs:     tail -f logs/bot.log"
    echo "  Stop bot:      ./stop-24-7.sh"
    echo "  Check status:  ps aux | grep telegram_simple"
    echo "  Restart bot:   ./restart-24-7.sh"
else
    echo "❌ Failed to start the bot. Check logs/bot.log for details."
    cat logs/bot.log
    exit 1
fi
