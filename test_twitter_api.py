#!/usr/bin/env python3
"""
Test script voor Twitter API credentials
"""

import os
from dotenv import load_dotenv
import tweepy

def test_twitter_credentials():
    """Test Twitter API credentials"""
    
    # Laad environment variables
    load_dotenv()
    
    # Haal Twitter credentials op
    bearer_token = os.getenv('TWITTER_BEARER_TOKEN')
    api_key = os.getenv('TWITTER_API_KEY')
    api_secret = os.getenv('TWITTER_API_SECRET')
    access_token = os.getenv('TWITTER_ACCESS_TOKEN')
    access_token_secret = os.getenv('TWITTER_ACCESS_TOKEN_SECRET')
    
    print("🐦 Twitter API Credentials Test")
    print("=" * 40)
    
    # Check of credentials aanwezig zijn
    print(f"✅ Bearer Token: {'✓' if bearer_token else '✗'}")
    print(f"✅ API Key: {'✓' if api_key else '✗'}")
    print(f"✅ API Secret: {'✓' if api_secret else '✗'}")
    print(f"✅ Access Token: {'✓' if access_token else '✗'}")
    print(f"✅ Access Token Secret: {'✓' if access_token_secret else '✗'}")
    print()
    
    if not all([bearer_token, api_key, api_secret, access_token, access_token_secret]):
        print("❌ Niet alle credentials zijn ingesteld!")
        return False
    
    try:
        # Test Bearer Token (v2 API)
        print("🔍 Testing Bearer Token (v2 API)...")
        client = tweepy.Client(bearer_token=bearer_token)
        
        # Test basic API access
        try:
            # Probeer een eenvoudige query
            tweets = client.search_recent_tweets(query="bitcoin", max_results=10)
            if tweets and tweets.data:
                print(f"✅ Bearer Token werkt! Gevonden {len(tweets.data)} tweets")
            else:
                print("⚠️ Bearer Token werkt, maar geen tweets gevonden")
        except Exception as e:
            print(f"❌ Bearer Token error: {e}")
    
    except Exception as e:
        print(f"❌ Twitter API v2 error: {e}")
    
    try:
        # Test v1.1 API
        print("\n🔍 Testing v1.1 API...")
        auth = tweepy.OAuthHandler(api_key, api_secret)
        auth.set_access_token(access_token, access_token_secret)
        api = tweepy.API(auth, wait_on_rate_limit=True)
        
        # Test API access
        try:
            # Probeer account info op te halen
            user = api.verify_credentials()
            if user:
                print(f"✅ v1.1 API werkt! Ingelogd als: @{user.screen_name}")
            else:
                print("❌ v1.1 API authenticatie mislukt")
        except Exception as e:
            print(f"❌ v1.1 API error: {e}")
            
    except Exception as e:
        print(f"❌ Twitter API v1.1 setup error: {e}")
    
    print("\n📊 **RESULTAAT:**")
    print("De Twitter API credentials zijn bijgewerkt in .env")
    print("Sommige functies hebben mogelijk beperkte toegang (gratis tier)")
    print("Dit is normaal en beïnvloedt de kernfunctionaliteit niet")
    
    return True

if __name__ == "__main__":
    test_twitter_credentials()
