"""
Configuration Settings Module
"""
import os
from dotenv import load_dotenv
from pathlib import Path

class Settings:
    def __init__(self):
        # Load environment variables
        load_dotenv()
        
        # Trading Settings
        self.TRADING_INTERVAL = 60  # seconds
        self.ERROR_RETRY_DELAY = 30  # seconds
        self.MAX_TRADES_PER_DAY = 10
        self.RISK_PER_TRADE = 0.02  # 2% risk per trade
        
        # Exchange API Settings
        self.KUCOIN_API_KEY = os.getenv('KUCOIN_API_KEY')
        self.KUCOIN_SECRET_KEY = os.getenv('KUCOIN_SECRET_KEY')
        self.KUCOIN_PASSPHRASE = os.getenv('KUCOIN_PASSPHRASE')
        self.KUCOIN_SANDBOX = os.getenv('KUCOIN_SANDBOX', 'False').lower() == 'true'
        
        self.MEXC_API_KEY = os.getenv('MEXC_API_KEY')
        self.MEXC_SECRET_KEY = os.getenv('MEXC_SECRET_KEY')
        self.MEXC_SANDBOX = os.getenv('MEXC_SANDBOX', 'False').lower() == 'true'
        
        # Database Settings
        self.DATABASE_URL = os.getenv('DATABASE_URL', 'sqlite:///trading_bot.db')
        
        # Logging Settings
        self.LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
        self.LOG_FILE = os.getenv('LOG_FILE', 'trading_bot.log')
        
        # Security Settings
        self.ENCRYPTION_KEY = os.getenv('ENCRYPTION_KEY')
        
        # Notification Settings
        self.TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
        # Parse Telegram admin user IDs as integers
        self.telegram_admin_user_ids = [
            int(x.strip()) for x in os.getenv("TELEGRAM_ADMIN_USER_ID", "").split(",") if x.strip()
        ]
        
        # Analysis Settings
        self.SENTIMENT_ANALYSIS_ENABLED = os.getenv('SENTIMENT_ANALYSIS_ENABLED', 'true').lower() == 'true'
        self.SENTIMENT_WEIGHT = float(os.getenv('SENTIMENT_WEIGHT', '0.3'))
        self.ADVANCED_INDICATORS_ENABLED = os.getenv('ADVANCED_INDICATORS_ENABLED', 'true').lower() == 'true'
        self.CUSTOM_INDICATORS = os.getenv('CUSTOM_INDICATORS', 'RSI,MACD,Bollinger').split(',')
        self.INDICATOR_TIMEFRAMES = os.getenv('INDICATOR_TIMEFRAMES', '1h,4h,1d').split(',')
        
        # Risk Management Settings
        self.MAX_POSITION_SIZE = float(os.getenv('MAX_POSITION_SIZE', '0.1'))  # 10% of portfolio
        self.STOP_LOSS_PERCENT = float(os.getenv('STOP_LOSS_PERCENT', '0.05'))  # 5% stop loss
        self.TAKE_PROFIT_PERCENT = float(os.getenv('TAKE_PROFIT_PERCENT', '0.15'))  # 1e5% take profit
        self.MAX_DRAWDOWN = float(os.getenv('MAX_DRAWDOWN', '0.20'))  # 20% max drawdown
        self.MAX_DAILY_TRADES = int(os.getenv('MAX_DAILY_TRADES', '10'))
        self.RISK_PER_TRADE = float(os.getenv('RISK_PER_TRADE', '0.02'))  # 2% risk per trade
        self.MAX_OPEN_POSITIONS = int(os.getenv('MAX_OPEN_POSITIONS', '5'))
        self.POSITION_SIZING_METHOD = os.getenv('POSITION_SIZING_METHOD', 'risk_based')
        
        # Advanced Risk Settings
        self.MAX_DAILY_LOSS = float(os.getenv('MAX_DAILY_LOSS', '0.05'))  # 5% max daily loss
        self.TRAILING_STOP_ENABLED = os.getenv('TRAILING_STOP_ENABLED', 'false').lower() == 'true'
        self.TRAILING_STOP_PERCENT = float(os.getenv('TRAILING_STOP_PERCENT', '0.02'))  # 2% trailing stop
        self.POSITION_SCALING_ENABLED = os.getenv('POSITION_SCALING_ENABLED', 'false').lower() == 'true'
        self.SCALE_IN_LEVELS = [float(x) for x in os.getenv('SCALE_IN_LEVELS', '0.01,0.02,0.03').split(',')]
        self.SCALE_OUT_LEVELS = [float(x) for x in os.getenv('SCALE_OUT_LEVELS', '0.02,0.03,0.05').split(',')]

        # Exchange Settings
        self.DEFAULT_EXCHANGE = 'kucoin'
        self.SUPPORTED_EXCHANGES = ['kucoin', 'mexc']
        
        # Trading Pairs Settings
        self.TRADING_PAIRS = ['BTC/USDT', 'ETH/USDT']  # Default trading pairs
        self.MIN_VOLUME_24H = 1000000  # Minimum 24h volume in USDT
