#!/usr/bin/env python3
"""
Eenvoudige Telegram Bot met error handling
"""
import asyncio
import sys
import traceback

def check_dependencies():
    """Check if all dependencies are available"""
    missing = []

    try:
        import aiohttp
    except ImportError:
        missing.append('aiohttp')

    try:
        import ccxt
    except ImportError:
        missing.append('ccxt')

    try:
        from loguru import logger
    except ImportError:
        missing.append('loguru')

    try:
        from dotenv import load_dotenv
    except ImportError:
        missing.append('python-dotenv')

    return missing

async def simple_telegram_test():
    """Simple test of Telegram bot functionality"""
    try:
        print("🧪 Testing Telegram bot components...")

        # Test config - import directly from root config.py
        import importlib.util
        spec = importlib.util.spec_from_file_location("root_config", "./config.py")
        root_config = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(root_config)
        settings = root_config.get_settings()
        print("✅ Config loaded")

        if not settings.telegram_bot_token:
            print("❌ No Telegram bot token found in .env")
            return False

        print(f"✅ Bot token found: {settings.telegram_bot_token[:10]}...")

        # Test HTTP
        import aiohttp
        import json

        base_url = f"https://api.telegram.org/bot{settings.telegram_bot_token}"

        async with aiohttp.ClientSession() as session:
            # Test bot token
            url = f"{base_url}/getMe"
            async with session.get(url) as response:
                result = await response.json()

                if result.get('ok'):
                    bot_info = result['result']
                    print(f"✅ Bot connected: @{bot_info['username']}")
                    return True
                else:
                    print(f"❌ Bot token invalid: {result}")
                    return False

    except Exception as e:
        print(f"❌ Telegram test failed: {e}")
        traceback.print_exc()
        return False

async def test_exchanges():
    """Test exchange connections"""
    try:
        print("\n🏦 Testing exchange connections...")

        from exchanges.manager import ExchangeManager
        exchange_manager = ExchangeManager()

        # Connect to exchanges
        results = await exchange_manager.connect_all()

        for exchange, connected in results.items():
            if connected:
                print(f"✅ {exchange.upper()} connected")
            else:
                print(f"❌ {exchange.upper()} failed")

        return any(results.values())

    except Exception as e:
        print(f"❌ Exchange test failed: {e}")
        traceback.print_exc()
        return False

async def start_telegram_bot():
    """Start the Telegram bot with error handling"""
    try:
        print("\n🚀 Starting Telegram bot...")

        from telegram_simple import SimpleTelegramBot
        bot = SimpleTelegramBot()

        print("✅ Bot instance created")

        # Run the bot
        await bot.run()

    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        print(f"\n❌ Bot error: {e}")
        traceback.print_exc()

        print("\n💡 Troubleshooting tips:")
        print("1. Check your .env file")
        print("2. Verify API keys")
        print("3. Check internet connection")
        print("4. Try: python debug_bot.py")

async def start_console_bot():
    """Start the console bot with error handling"""
    try:
        print("\n🚀 Starting Console bot...")

        from simple_bot import SimpleTradingBot
        bot = SimpleTradingBot()

        if not await bot.initialize():
            print("❌ Failed to initialize bot")
            return

        print("✅ Bot initialized successfully!")
        await bot.run_console_interface()

    except KeyboardInterrupt:
        print("\n👋 Bot stopped by user")
    except Exception as e:
        print(f"\n❌ Bot error: {e}")
        traceback.print_exc()

def main():
    """Main function with error handling"""
    print("🤖 Trading Bot Launcher")
    print("=" * 30)

    # Check dependencies
    missing = check_dependencies()
    if missing:
        print(f"❌ Missing dependencies: {', '.join(missing)}")
        print("Install with:")
        print(f"pip install {' '.join(missing)}")
        return

    print("✅ All dependencies found")

    # Choose bot type
    print("\n🎯 Choose bot type:")
    print("1. Telegram Bot (with buttons)")
    print("2. Console Bot (terminal)")
    print("3. Test connections only")
    print("4. Debug mode")

    try:
        choice = input("\nEnter choice (1-4): ").strip()

        if choice == "1":
            # Test Telegram first
            if asyncio.run(simple_telegram_test()):
                asyncio.run(start_telegram_bot())
            else:
                print("❌ Telegram test failed")

        elif choice == "2":
            asyncio.run(start_console_bot())

        elif choice == "3":
            asyncio.run(test_exchanges())

        elif choice == "4":
            import subprocess
            subprocess.run([sys.executable, "debug_bot.py"])

        else:
            print("❌ Invalid choice")

    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
