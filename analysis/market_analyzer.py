"""
Automated Market Analysis - Runs every 5 minutes
"""
import asyncio
import time
from typing import Dict, List
from datetime import datetime, timedelta
from decimal import Decimal
from loguru import logger
import pandas as pd

# Try to import pandas_ta, but make it optional for Python 3.13 compatibility
try:
    # Fix NumPy compatibility issue
    import numpy as np
    # Ensure NaN is available for pandas_ta
    if not hasattr(np, 'NaN'):
        np.NaN = np.nan

    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
    logger.info("✅ pandas_ta imported successfully")
except ImportError as e:
    logger.warning(f"⚠️ pandas_ta not available (ImportError): {e}")
    PANDAS_TA_AVAILABLE = False
    ta = None
except Exception as e:
    logger.warning(f"⚠️ pandas_ta not available (Other error): {e}")
    PANDAS_TA_AVAILABLE = False
    ta = None

# Create fallback functions if pandas_ta is not available
if not PANDAS_TA_AVAILABLE:
    class DummyTA:
        @staticmethod
        def rsi(close, length=14, **kwargs):
            """Simple RSI calculation fallback"""
            try:
                if len(close) < length + 1:
                    return pd.Series([50] * len(close), index=close.index)

                delta = close.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=length).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=length).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                return rsi
            except:
                return pd.Series([50] * len(close), index=close.index)

        @staticmethod
        def macd(close, fast=12, slow=26, signal=9, **kwargs):
            """Simple MACD calculation fallback"""
            try:
                ema_fast = close.ewm(span=fast).mean()
                ema_slow = close.ewm(span=slow).mean()
                macd_line = ema_fast - ema_slow
                signal_line = macd_line.ewm(span=signal).mean()
                histogram = macd_line - signal_line

                result = pd.DataFrame({
                    f'MACD_{fast}_{slow}_{signal}': macd_line,
                    f'MACDs_{fast}_{slow}_{signal}': signal_line,
                    f'MACDh_{fast}_{slow}_{signal}': histogram
                })
                return result
            except:
                return None

        @staticmethod
        def bbands(close, length=20, std=2, **kwargs):
            """Simple Bollinger Bands calculation fallback"""
            try:
                sma = close.rolling(window=length).mean()
                rolling_std = close.rolling(window=length).std()
                upper = sma + (rolling_std * std)
                lower = sma - (rolling_std * std)

                result = pd.DataFrame({
                    f'BBL_{length}_{std}': lower,
                    f'BBM_{length}_{std}': sma,
                    f'BBU_{length}_{std}': upper
                })
                return result
            except:
                return None

        @staticmethod
        def adx(high, low, close, length=14, **kwargs):
            """Simple ADX calculation fallback"""
            try:
                # Simplified ADX calculation
                tr1 = high - low
                tr2 = abs(high - close.shift())
                tr3 = abs(low - close.shift())
                tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
                atr = tr.rolling(window=length).mean()

                # Simplified directional movement
                plus_dm = (high - high.shift()).where((high - high.shift()) > (low.shift() - low), 0)
                minus_dm = (low.shift() - low).where((low.shift() - low) > (high - high.shift()), 0)

                plus_di = 100 * (plus_dm.rolling(window=length).mean() / atr)
                minus_di = 100 * (minus_dm.rolling(window=length).mean() / atr)

                dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
                adx = dx.rolling(window=length).mean()

                result = pd.DataFrame({
                    f'ADX_{length}': adx,
                    f'DMP_{length}': plus_di,
                    f'DMN_{length}': minus_di
                })
                return result
            except:
                return None

        @staticmethod
        def stoch(*args, **kwargs):
            return None

        @staticmethod
        def cci(*args, **kwargs):
            return None

    ta = DummyTA()

from .technical_simple import TechnicalAnalyzer
from .ai_analyzer import AIAnalyzer
from exchanges.manager import ExchangeManager
from config.settings import Settings

class MarketAnalyzer:
    """Automated market analysis that runs every 5 minutes"""

    def __init__(self, exchange_manager: ExchangeManager):
        self.exchange_manager = exchange_manager
        self.technical_analyzer = TechnicalAnalyzer()
        self.ai_analyzer = AIAnalyzer()
        self.settings = Settings()

        # Analysis settings
        self.analysis_interval = 300  # 5 minutes (optimized for better opportunities) in seconds
        self.symbols_to_analyze = ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT"]
        self.running = False

        # Store analysis results
        self.latest_analysis = {}
        self.analysis_history = {}

        # Alert thresholds
        self.price_change_alert_threshold = 5.0  # 5% price change
        self.volume_spike_threshold = 3.0  # 3x normal volume
        self.volatility_alert_threshold = 10.0  # 10% volatility

        # Augment: fine-tuned AI settings for optimal trading bot coding
        # (Pas aan naar wens, zie settings.json voor meer opties)
        # Voorbeeld: self.augment_features = {"chat": True, "autocomplete": True, "codeSearch": True, "docSearch": True}
        self.augment_features = {
            "chat": True,  # Snel vragen stellen over code of trading
            "autocomplete": True,  # Inline Python aanvulling
            "codeSearch": False,  # Alleen aanzetten als je veel zoekt
            "docSearch": True,  # Direct uitleg uit docstrings/README
            "modelRouting": {
                "python": "deepseek-coder-33b-instruct",
                "markdown": "mistral-7b-instruct",
                "default": "deepseek-coder-33b-instruct"
            },
            "temperature": 0.2,  # Veilige, voorspelbare code
            "max_context_length": 2048  # Genoeg context voor trading scripts
        }

    async def start_analysis(self):
        """Start the automated market analysis"""
        self.running = True
        logger.info("🔍 Starting automated market analysis (every 5 minutes)")

        while self.running:
            try:
                start_time = time.time()

                # Perform analysis for all symbols
                await self._perform_market_analysis()

                # Check for alerts
                await self._check_market_alerts()

                # Calculate next run time
                elapsed_time = time.time() - start_time
                sleep_time = max(0, self.analysis_interval - elapsed_time)

                logger.info(f"📊 Market analysis completed in {elapsed_time:.2f}s. Next analysis in {sleep_time:.0f}s")

                if self.running:
                    await asyncio.sleep(sleep_time)

            except Exception as e:
                logger.error(f"Error in market analysis loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retrying

    def stop_analysis(self):
        """Stop the automated market analysis"""
        self.running = False
        logger.info("🛑 Stopping automated market analysis")

    async def _perform_market_analysis(self):
        """Perform comprehensive market analysis"""
        analysis_timestamp = datetime.now()

        try:
            if not self.symbols_to_analyze:
                logger.warning("No symbols configured for analysis")
                return

            for symbol in self.symbols_to_analyze:
                try:
                    # Get market data with timeout protection
                    market_data = await asyncio.wait_for(
                        self._get_comprehensive_market_data(symbol),
                        timeout=30
                    )
                    if not market_data:
                        logger.warning(f"No market data available for {symbol}")
                        continue

                    # Validate critical data points
                    if not self._validate_market_data(market_data):
                        logger.error(f"Invalid market data for {symbol}")
                        continue

                    # Get historical data for advanced indicators
                    historical_data = await self._get_historical_data(symbol)

                    # Technical analysis
                    technical_analysis = await self.technical_analyzer.analyze(symbol, market_data)

                    # Add advanced indicators if historical data is available
                    if not historical_data.empty:
                        latest_data = historical_data.iloc[-1]
                        technical_analysis['adx'] = latest_data.get('adx', 0)
                        technical_analysis['bb_width'] = latest_data.get('bb_width', 0)

                        # Add ADX interpretation
                        adx_value = latest_data.get('adx', 0)
                        if adx_value > 25:
                            technical_analysis['adx_signal'] = 'strong_trend'
                        elif adx_value > 20:
                            technical_analysis['adx_signal'] = 'trending'
                        else:
                            technical_analysis['adx_signal'] = 'sideways'

                        # Add Bollinger Bands width interpretation
                        bb_width = latest_data.get('bb_width', 0)
                        if bb_width > 0.1:
                            technical_analysis['bb_signal'] = 'high_volatility'
                        elif bb_width < 0.05:
                            technical_analysis['bb_signal'] = 'low_volatility'
                        else:
                            technical_analysis['bb_signal'] = 'normal_volatility'

                    # AI analysis
                    ai_analysis = await self.ai_analyzer.analyze_market(symbol, market_data)

                    # Combine analyses
                    combined_analysis = {
                        'timestamp': analysis_timestamp,
                        'symbol': symbol,
                        'market_data': market_data,
                        'technical': technical_analysis,
                        'ai': ai_analysis,
                        'alerts': await self._generate_alerts(symbol, market_data, technical_analysis, ai_analysis)
                    }

                    # Store results
                    self.latest_analysis[symbol] = combined_analysis

                    # Store in history
                    if symbol not in self.analysis_history:
                        self.analysis_history[symbol] = []

                    self.analysis_history[symbol].append(combined_analysis)

                    # Keep only last 100 analyses per symbol
                    if len(self.analysis_history[symbol]) > 100:
                        self.analysis_history[symbol] = self.analysis_history[symbol][-100:]

                    logger.debug(f"✅ Analysis completed for {symbol}")

                except asyncio.TimeoutError:
                    logger.error(f"Timeout while analyzing {symbol}")
                except Exception as e:
                    logger.error(f"Error analyzing {symbol}: {e}")

        except Exception as e:
            logger.error(f"Error in market analysis: {e}")

    async def _get_comprehensive_market_data(self, symbol: str) -> Dict:
        """Get comprehensive market data from all exchanges"""
        try:
            # Get tickers from all exchanges
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)

            if not tickers:
                return {}

            # Use the exchange with the highest volume
            best_ticker = None
            highest_volume = 0

            for exchange_name, ticker in tickers.items():
                if ticker.volume > highest_volume:
                    highest_volume = ticker.volume
                    best_ticker = ticker

            if not best_ticker:
                return {}

            # Create comprehensive market data
            market_data = {
                'symbol': symbol,
                'price': float(best_ticker.last),
                'bid': float(best_ticker.bid),
                'ask': float(best_ticker.ask),
                'high_24h': float(best_ticker.high),
                'low_24h': float(best_ticker.low),
                'volume': float(best_ticker.volume),
                'timestamp': best_ticker.timestamp,

                # Calculate additional metrics
                'spread': float((best_ticker.ask - best_ticker.bid) / best_ticker.bid * 100) if best_ticker.bid > 0 else 0,
                'price_change_24h': float((best_ticker.last - best_ticker.low) / best_ticker.low * 100) if best_ticker.low > 0 else 0,

                # Exchange comparison
                'exchange_prices': {name: float(ticker.last) for name, ticker in tickers.items()},
                'price_variance': self._calculate_price_variance(tickers),

                # Volume analysis
                'avg_volume_24h': float(best_ticker.volume),  # Simplified
                'volume_24h_usd': float(best_ticker.volume * best_ticker.last),
            }

            return market_data

        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return {}

    def _validate_market_data(self, market_data: Dict) -> bool:
        """Validate that market data contains required fields"""
        try:
            required_fields = ['symbol', 'price', 'volume']
            for field in required_fields:
                if field not in market_data:
                    logger.warning(f"Missing required field: {field}")
                    return False

                value = market_data[field]
                if value is None or (isinstance(value, (int, float)) and (value <= 0 or not isinstance(value, (int, float)))):
                    logger.warning(f"Invalid value for {field}: {value}")
                    return False

            return True
        except Exception as e:
            logger.error(f"Error validating market data: {e}")
            return False

    def _calculate_price_variance(self, tickers: Dict) -> float:
        """Calculate price variance across exchanges"""
        try:
            prices = [float(ticker.last) for ticker in tickers.values()]
            if len(prices) < 2:
                return 0.0

            avg_price = sum(prices) / len(prices)
            variance = sum((price - avg_price) ** 2 for price in prices) / len(prices)

            return (variance ** 0.5) / avg_price * 100  # Coefficient of variation as percentage

        except Exception:
            return 0.0

    def _calculate_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate ADX and Bollinger Bands width indicators"""
        try:
            if len(df) < 20:
                # Not enough data for meaningful calculations
                df['adx'] = 25.0
                df['bb_width'] = 0.05
                return df

            # Calculate ADX (Average Directional Index)
            try:
                adx_result = ta.adx(df['high'], df['low'], df['close'])
                if adx_result is not None:
                    if isinstance(adx_result, pd.DataFrame) and 'ADX_14' in adx_result.columns:
                        df['adx'] = adx_result['ADX_14'].fillna(25.0)
                    elif isinstance(adx_result, pd.Series):
                        df['adx'] = adx_result.fillna(25.0)
                    else:
                        df['adx'] = 25.0
                else:
                    df['adx'] = 25.0
            except Exception as e:
                logger.debug(f"ADX calculation failed, using fallback: {e}")
                df['adx'] = 25.0

            # Calculate Bollinger Bands and width
            try:
                bb = ta.bbands(df['close'], length=20, std=2)
                if bb is not None and isinstance(bb, pd.DataFrame):
                    # Check for different possible column names
                    upper_col = None
                    lower_col = None
                    middle_col = None

                    for col in bb.columns:
                        if 'BBU' in col or 'upper' in col.lower():
                            upper_col = col
                        elif 'BBL' in col or 'lower' in col.lower():
                            lower_col = col
                        elif 'BBM' in col or 'middle' in col.lower():
                            middle_col = col

                    if upper_col and lower_col and middle_col:
                        df['bb_width'] = ((bb[upper_col] - bb[lower_col]) / bb[middle_col]).fillna(0.05)
                    else:
                        # Fallback calculation
                        rolling_mean = df['close'].rolling(window=20).mean()
                        rolling_std = df['close'].rolling(window=20).std()
                        bb_upper = rolling_mean + (rolling_std * 2)
                        bb_lower = rolling_mean - (rolling_std * 2)
                        df['bb_width'] = ((bb_upper - bb_lower) / rolling_mean).fillna(0.05)
                else:
                    # Fallback calculation
                    rolling_mean = df['close'].rolling(window=20).mean()
                    rolling_std = df['close'].rolling(window=20).std()
                    bb_upper = rolling_mean + (rolling_std * 2)
                    bb_lower = rolling_mean - (rolling_std * 2)
                    df['bb_width'] = ((bb_upper - bb_lower) / rolling_mean).fillna(0.05)
            except Exception as e:
                logger.debug(f"Bollinger Bands calculation failed, using fallback: {e}")
                # Simple fallback calculation
                if len(df) >= 20:
                    rolling_mean = df['close'].rolling(window=20).mean()
                    rolling_std = df['close'].rolling(window=20).std()
                    bb_upper = rolling_mean + (rolling_std * 2)
                    bb_lower = rolling_mean - (rolling_std * 2)
                    df['bb_width'] = ((bb_upper - bb_lower) / rolling_mean).fillna(0.05)
                else:
                    df['bb_width'] = 0.05

            # Ensure no infinite or NaN values
            df['adx'] = df['adx'].replace([float('inf'), -float('inf')], 25.0).fillna(25.0)
            df['bb_width'] = df['bb_width'].replace([float('inf'), -float('inf')], 0.05).fillna(0.05)

            return df

        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            # Return df with safe default values
            df['adx'] = 25.0
            df['bb_width'] = 0.05
            return df

    async def _get_historical_data(self, symbol: str, timeframe: str = '1h', limit: int = 100) -> pd.DataFrame:
        """Get historical OHLCV data for technical analysis"""
        try:
            # Try to get historical data from the first available exchange
            for exchange_name, exchange in self.exchange_manager.exchanges.items():
                if hasattr(exchange, 'fetch_ohlcv'):
                    try:
                        ohlcv = await exchange.fetch_ohlcv(symbol, timeframe, limit=limit)

                        if ohlcv:
                            # Convert to DataFrame
                            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                            df.set_index('timestamp', inplace=True)

                            # Calculate indicators
                            df = self._calculate_indicators(df)

                            return df

                    except Exception as e:
                        logger.debug(f"Failed to get historical data from {exchange_name}: {e}")
                        continue

            # If no historical data available, create empty DataFrame
            logger.debug(f"No historical data available for {symbol} - using current price only")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error getting historical data for {symbol}: {e}")
            return pd.DataFrame()

    async def _generate_alerts(self, symbol: str, market_data: Dict,
                             technical_analysis: Dict, ai_analysis: Dict) -> List[Dict]:
        """Generate market alerts based on analysis"""
        alerts = []

        try:
            # Price change alerts
            price_change = abs(market_data.get('price_change_24h', 0))
            if price_change > self.price_change_alert_threshold:
                alerts.append({
                    'type': 'price_movement',
                    'severity': 'high' if price_change > 10 else 'medium',
                    'message': f"{symbol} moved {price_change:.2f}% in 24h",
                    'value': price_change
                })

            # Volume spike alerts
            volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume_24h', volume)

            if avg_volume > 0:
                volume_ratio = volume / avg_volume
                if volume_ratio > self.volume_spike_threshold:
                    alerts.append({
                        'type': 'volume_spike',
                        'severity': 'high' if volume_ratio > 5 else 'medium',
                        'message': f"{symbol} volume is {volume_ratio:.1f}x normal",
                        'value': volume_ratio
                    })

            # Technical alerts
            if 'rsi' in technical_analysis:
                rsi = technical_analysis['rsi']
                if rsi > 80:
                    alerts.append({
                        'type': 'technical',
                        'severity': 'medium',
                        'message': f"{symbol} RSI extremely overbought ({rsi:.1f})",
                        'value': rsi
                    })
                elif rsi < 20:
                    alerts.append({
                        'type': 'technical',
                        'severity': 'medium',
                        'message': f"{symbol} RSI extremely oversold ({rsi:.1f})",
                        'value': rsi
                    })

            # ADX alerts
            if 'adx' in technical_analysis:
                adx = technical_analysis['adx']
                adx_signal = technical_analysis.get('adx_signal', '')

                if adx > 30 and adx_signal == 'strong_trend':
                    alerts.append({
                        'type': 'technical',
                        'severity': 'medium',
                        'message': f"{symbol} ADX shows strong trend ({adx:.1f})",
                        'value': adx
                    })
                elif adx < 15:
                    alerts.append({
                        'type': 'technical',
                        'severity': 'low',
                        'message': f"{symbol} ADX shows weak trend/sideways ({adx:.1f})",
                        'value': adx
                    })

            # Bollinger Bands width alerts
            if 'bb_width' in technical_analysis:
                bb_width = technical_analysis['bb_width']
                bb_signal = technical_analysis.get('bb_signal', '')

                if bb_signal == 'low_volatility':
                    alerts.append({
                        'type': 'technical',
                        'severity': 'medium',
                        'message': f"{symbol} Bollinger Bands squeeze - potential breakout ({bb_width:.4f})",
                        'value': bb_width
                    })
                elif bb_signal == 'high_volatility':
                    alerts.append({
                        'type': 'technical',
                        'severity': 'medium',
                        'message': f"{symbol} High volatility detected ({bb_width:.4f})",
                        'value': bb_width
                    })

            # AI alerts
            ai_confidence = ai_analysis.get('confidence', 0)
            ai_recommendation = ai_analysis.get('recommendation', 'hold')

            if ai_confidence > 0.8 and ai_recommendation in ['strong_buy', 'strong_sell']:
                alerts.append({
                    'type': 'ai_signal',
                    'severity': 'high',
                    'message': f"{symbol} AI recommends {ai_recommendation} (confidence: {ai_confidence:.2f})",
                    'value': ai_confidence
                })

            # Price variance alert (arbitrage opportunity)
            price_variance = market_data.get('price_variance', 0)
            if price_variance > 2.0:  # More than 2% variance between exchanges
                alerts.append({
                    'type': 'arbitrage',
                    'severity': 'medium',
                    'message': f"{symbol} price variance {price_variance:.2f}% across exchanges",
                    'value': price_variance
                })

            return alerts

        except Exception as e:
            logger.error(f"Error generating alerts for {symbol}: {e}")
            return []

    async def _check_market_alerts(self):
        """Check and log market alerts"""
        try:
            all_alerts = []

            for symbol, analysis in self.latest_analysis.items():
                alerts = analysis.get('alerts', [])
                for alert in alerts:
                    alert['symbol'] = symbol
                    all_alerts.append(alert)

            # Sort by severity
            severity_order = {'high': 3, 'medium': 2, 'low': 1}
            all_alerts.sort(key=lambda x: severity_order.get(x.get('severity', 'low'), 1), reverse=True)

            # Log alerts
            if all_alerts:
                logger.info(f"🚨 {len(all_alerts)} market alerts detected:")
                for alert in all_alerts[:10]:  # Show top 10 alerts
                    severity_emoji = {'high': '🔴', 'medium': '🟡', 'low': '🟢'}.get(alert.get('severity', 'low'), '⚪')
                    logger.info(f"  {severity_emoji} {alert['message']}")

        except Exception as e:
            logger.error(f"Error checking market alerts: {e}")

    def get_latest_analysis(self, symbol: str = None) -> Dict:
        """Get latest analysis for a symbol or all symbols"""
        if symbol:
            return self.latest_analysis.get(symbol, {})
        return self.latest_analysis

    def get_analysis_summary(self) -> Dict:
        """Get summary of latest market analysis"""
        try:
            summary = {
                'timestamp': datetime.now(),
                'symbols_analyzed': len(self.latest_analysis),
                'total_alerts': 0,
                'high_severity_alerts': 0,
                'market_overview': {}
            }

            for symbol, analysis in self.latest_analysis.items():
                alerts = analysis.get('alerts', [])
                summary['total_alerts'] += len(alerts)
                summary['high_severity_alerts'] += len([a for a in alerts if a.get('severity') == 'high'])

                # Market overview
                market_data = analysis.get('market_data', {})
                ai_analysis = analysis.get('ai', {})

                summary['market_overview'][symbol] = {
                    'price': market_data.get('price', 0),
                    'price_change_24h': market_data.get('price_change_24h', 0),
                    'ai_recommendation': ai_analysis.get('recommendation', 'hold'),
                    'ai_confidence': ai_analysis.get('confidence', 0),
                    'alerts': len(alerts)
                }

            return summary

        except Exception as e:
            logger.error(f"Error generating analysis summary: {e}")
            return {}

    async def get_market_report(self) -> str:
        """Generate a formatted market report"""
        try:
            summary = self.get_analysis_summary()

            report = f"📊 **Market Analysis Report**\n"
            report += f"🕐 {summary['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}\n\n"

            report += f"📈 **Overview:**\n"
            report += f"• Symbols analyzed: {summary['symbols_analyzed']}\n"
            report += f"• Total alerts: {summary['total_alerts']}\n"
            report += f"• High priority alerts: {summary['high_severity_alerts']}\n\n"

            report += f"💰 **Market Status:**\n"

            for symbol, data in summary['market_overview'].items():
                price_change = data['price_change_24h']
                change_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"

                report += f"{change_emoji} **{symbol}**: ${data['price']:.4f} "
                report += f"({price_change:+.2f}%)\n"
                report += f"   AI: {data['ai_recommendation']} ({data['ai_confidence']:.2f})\n"

                if data['alerts'] > 0:
                    report += f"   🚨 {data['alerts']} alerts\n"
                report += "\n"

            return report

        except Exception as e:
            logger.error(f"Error generating market report: {e}")
            return "❌ Error generating market report"

    def _validate_market_data(self, market_data: Dict) -> bool:
        """Validate market data consistency"""
        required_fields = ['price', 'volume', 'timestamp']

        try:
            # Check required fields
            for field in required_fields:
                if field not in market_data:
                    logger.warning(f"Missing required field: {field}")
                    return False

            # Validate price and volume
            if not isinstance(market_data['price'], (int, float)) or market_data['price'] <= 0:
                logger.warning("Invalid price value")
                return False

            if not isinstance(market_data['volume'], (int, float)) or market_data['volume'] < 0:
                logger.warning("Invalid volume value")
                return False

            # Check timestamp freshness
            if time.time() - market_data['timestamp'] > 300:  # 5 minutes
                logger.warning("Market data too old")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating market data: {e}")
            return False
