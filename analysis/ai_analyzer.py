"""
AI-powered Market Analysis Module
"""
import asyncio
import json
import aiohttp
from typing import Dict, List, Optional
from decimal import Decimal
import statistics
from loguru import logger
from config.settings import Settings
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage
import tweepy
import praw
import os
from datetime import datetime, timedelta
from newsapi import NewsApiClient

class AIAnalyzer:
    """AI-powered market analysis using multiple models and data sources"""

    def __init__(self):
        self.settings = Settings()
        self.session = None

        # Initialize LangChain
        try:
            self.llm = ChatOpenAI(
                openai_api_base="http://127.0.0.1:1234/v1",
                openai_api_key="local",
                model_name="deepseek-coder-33b-instruct",
            )
            logger.info("✅ LangChain AI initialized successfully")
        except Exception as e:
            logger.error(f"❌ Error initializing LangChain: {e}")
            self.llm = None

        # Initialize Twitter API (optional)
        try:
            twitter_key = os.getenv('TWITTER_API_KEY')
            twitter_secret = os.getenv('TWITTER_API_SECRET')
            if twitter_key and twitter_secret:
                auth = tweepy.OAuthHandler(twitter_key, twitter_secret)
                self.twitter_api = tweepy.API(auth)
                logger.info("✅ Twitter API initialized successfully")
            else:
                self.twitter_api = None
                logger.info("ℹ️ Twitter API not configured (optional)")
        except Exception as e:
            logger.warning(f"⚠️ Twitter API initialization failed: {e}")
            self.twitter_api = None

        # Initialize Reddit API (optional)
        try:
            reddit_id = os.getenv('REDDIT_CLIENT_ID')
            reddit_secret = os.getenv('REDDIT_CLIENT_SECRET')
            if reddit_id and reddit_secret:
                self.reddit_api = praw.Reddit(
                    client_id=reddit_id,
                    client_secret=reddit_secret,
                    username=os.getenv('REDDIT_USERNAME'),
                    password=os.getenv('REDDIT_PASSWORD'),
                    user_agent='MyOwnMoneyMaker/1.0'
                )
                logger.info("✅ Reddit API initialized successfully")
            else:
                self.reddit_api = None
                logger.info("ℹ️ Reddit API not configured (optional)")
        except Exception as e:
            logger.warning(f"⚠️ Reddit API initialization failed: {e}")
            self.reddit_api = None

        # Initialize News API (optional)
        try:
            news_key = os.getenv('NEWS_API_KEY')
            if news_key:
                self.news_api = NewsApiClient(api_key=news_key)
                logger.info("✅ News API initialized successfully")
            else:
                self.news_api = None
                logger.info("ℹ️ News API not configured (optional)")
        except Exception as e:
            logger.warning(f"⚠️ News API initialization failed: {e}")
            self.news_api = None

    async def analyze_market(self, symbol: str, market_data: Dict) -> Dict:
        """Comprehensive AI market analysis"""
        try:
            # Sentiment analysis
            sentiment = await self.analyze_sentiment(symbol, market_data)

            # Trend analysis
            trend = await self.analyze_trend(symbol, market_data)

            # Pattern recognition
            patterns = await self.detect_patterns(symbol, market_data)

            # News impact analysis
            news_impact = await self.analyze_news_impact(symbol)

            # Social media sentiment
            social_sentiment = await self.analyze_social_sentiment(symbol)

            # Combine all AI analyses
            combined_analysis = {
                'sentiment': sentiment,
                'trend': trend,
                'patterns': patterns,
                'news_impact': news_impact,
                'social_sentiment': social_sentiment,
                'confidence': self._calculate_ai_confidence(sentiment, trend, patterns),
                'recommendation': self._generate_recommendation(sentiment, trend, patterns)
            }

            return combined_analysis

        except Exception as e:
            logger.error(f"Error in AI market analysis for {symbol}: {e}")
            return {
                'sentiment': {'score': 0.5, 'confidence': 0.0},
                'trend': {'direction': 'neutral', 'strength': 0.0},
                'patterns': [],
                'confidence': 0.0,
                'recommendation': 'hold'
            }

    async def analyze_sentiment(self, symbol: str, market_data: Dict) -> Dict:
        """Analyze market sentiment using AI"""
        try:
            # Combine multiple sentiment sources
            sentiment_sources = []

            # Technical sentiment
            tech_sentiment = await self._calculate_technical_sentiment(market_data)
            sentiment_sources.append(('technical', tech_sentiment, 0.4))

            # News sentiment
            news_sentiment = await self._analyze_news_sentiment(symbol)
            sentiment_sources.append(('news', news_sentiment, 0.3))

            # Social sentiment
            social_sentiment = await self._analyze_social_media_sentiment(symbol)
            sentiment_sources.append(('social', social_sentiment, 0.3))

            # Weighted average
            total_weight = sum(weight for _, _, weight in sentiment_sources)
            weighted_sentiment = sum(sentiment * weight for _, sentiment, weight in sentiment_sources) / total_weight

            # Calculate confidence based on agreement between sources
            sentiments = [sentiment for _, sentiment, _ in sentiment_sources]
            if len(sentiments) > 1:
                std_dev = statistics.stdev(sentiments)
                confidence = 1.0 - (std_dev / 0.5)  # Lower std = higher confidence
            else:
                confidence = 0.5

            return {
                'score': max(0.0, min(1.0, weighted_sentiment)),
                'confidence': max(0.0, min(1.0, confidence)),
                'sources': {source: sentiment for source, sentiment, _ in sentiment_sources}
            }

        except Exception as e:
            logger.error(f"Error in sentiment analysis: {e}")
            return {'score': 0.5, 'confidence': 0.0}

    async def analyze_trend(self, symbol: str, market_data: Dict) -> Dict:
        """AI-powered trend analysis"""
        try:
            # Price momentum analysis
            price_changes = [
                market_data.get('price_change_1h', 0),
                market_data.get('price_change_4h', 0),
                market_data.get('price_change_24h', 0),
                market_data.get('price_change_7d', 0)
            ]

            # Calculate trend strength
            positive_changes = sum(1 for change in price_changes if change > 0)
            trend_consistency = positive_changes / len(price_changes)

            # Volume trend
            volume_trend = await self._analyze_volume_trend(market_data)

            # Determine trend direction and strength
            if trend_consistency > 0.75:
                direction = 'bullish'
                strength = trend_consistency
            elif trend_consistency < 0.25:
                direction = 'bearish'
                strength = 1 - trend_consistency
            else:
                direction = 'neutral'
                strength = 0.5

            # Adjust strength based on volume
            strength = (strength + volume_trend) / 2

            return {
                'direction': direction,
                'strength': max(0.0, min(1.0, strength)),
                'consistency': trend_consistency,
                'volume_confirmation': volume_trend > 0.5
            }

        except Exception as e:
            logger.error(f"Error in trend analysis: {e}")
            return {'direction': 'neutral', 'strength': 0.0}

    async def detect_patterns(self, symbol: str, market_data: Dict) -> List[Dict]:
        """AI pattern recognition using both technical and AI analysis"""
        try:
            patterns = []

            # Get AI-powered pattern analysis
            ai_patterns = await self._analyze_patterns_with_ai(market_data)
            patterns.extend(ai_patterns)

            # Traditional technical pattern detection
            current_price = market_data['price']
            high_24h = market_data.get('high_24h', current_price)
            low_24h = market_data.get('low_24h', current_price)

            # Breakout pattern
            if current_price > high_24h * 0.98:
                patterns.append({
                    'type': 'breakout_resistance',
                    'confidence': 0.7,
                    'signal': 'bullish'
                })

            # Support bounce pattern
            if current_price < low_24h * 1.02:
                patterns.append({
                    'type': 'support_bounce',
                    'confidence': 0.6,
                    'signal': 'bullish'
                })

            # Consolidation pattern
            if high_24h > 0 and low_24h > 0:
                range_size = (high_24h - low_24h) / low_24h
                if range_size < 0.05:  # Less than 5% range
                    patterns.append({
                        'type': 'consolidation',
                        'confidence': 0.8,
                        'signal': 'neutral'
                    })

            # Advanced pattern analysis using AI
            ai_patterns = await self._analyze_patterns_with_ai(market_data)
            patterns.extend(ai_patterns)

            return patterns

        except Exception as e:
            logger.error(f"Error in pattern detection: {e}")
            return []

    async def analyze_news_impact(self, symbol: str) -> Dict:
        """Analyze news impact on price"""
        try:
            # In real implementation, fetch and analyze news
            # For now, return simulated analysis

            return {
                'impact_score': 0.5,  # -1 to 1
                'confidence': 0.6,
                'key_events': [],
                'sentiment': 'neutral'
            }

        except Exception as e:
            logger.error(f"Error in news impact analysis: {e}")
            return {'impact_score': 0.0, 'confidence': 0.0}

    async def analyze_social_sentiment(self, symbol: str) -> Dict:
        """Analyze social media sentiment"""
        try:
            # In real implementation, analyze Twitter, Reddit, etc.
            # For now, return simulated analysis

            return {
                'sentiment_score': 0.5,  # 0 to 1
                'confidence': 0.5,
                'volume': 'medium',
                'trending': False
            }

        except Exception as e:
            logger.error(f"Error in social sentiment analysis: {e}")
            return {'sentiment_score': 0.5, 'confidence': 0.0}

    async def _calculate_technical_sentiment(self, market_data: Dict) -> float:
        """Calculate sentiment from technical indicators"""
        try:
            sentiment = 0.5  # Neutral starting point

            # Price momentum
            price_change_24h = market_data.get('price_change_24h', 0)
            if price_change_24h > 5:
                sentiment += 0.2
            elif price_change_24h < -5:
                sentiment -= 0.2

            # Volume analysis
            volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume_24h', volume)

            if avg_volume > 0:
                volume_ratio = volume / avg_volume
                if volume_ratio > 1.5:
                    sentiment += 0.1  # High volume is positive
                elif volume_ratio < 0.5:
                    sentiment -= 0.1  # Low volume is negative

            return max(0.0, min(1.0, sentiment))

        except Exception:
            return 0.5

    async def _analyze_news_sentiment(self, symbol: str) -> float:
        """Analyze news sentiment using NewsAPI"""
        try:
            if not self.news_api:
                return 0.5

            # Rate limiting: Skip if called too recently
            if not hasattr(self, '_last_news_call'):
                self._last_news_call = {}

            import time
            current_time = time.time()
            if symbol in self._last_news_call:
                if current_time - self._last_news_call[symbol] < 600:  # 10 minutes
                    logger.debug(f"Skipping news analysis for {symbol} due to rate limiting")
                    return 0.5

            self._last_news_call[symbol] = current_time

            # Get news articles for the symbol
            crypto_name = symbol.split('/')[0]  # e.g., BTC from BTC/USDT

            articles = self.news_api.get_everything(
                q=f"{crypto_name} OR cryptocurrency",
                language='en',
                sort_by='relevancy',
                page_size=5,  # Reduced from 20
                from_param=(datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
            )

            if not articles['articles']:
                return 0.5

            sentiments = []

            # Analyze article sentiment (simplified)
            for article in articles['articles']:
                title = article['title'].lower()
                description = article['description'].lower() if article['description'] else ''

                # Keywords analysis
                bullish_keywords = ['bullish', 'surge', 'rally', 'growth', 'gain', 'positive', 'breakthrough']
                bearish_keywords = ['bearish', 'crash', 'drop', 'fall', 'decline', 'negative', 'risk']

                text = title + ' ' + description

                bullish_count = sum(1 for word in bullish_keywords if word in text)
                bearish_count = sum(1 for word in bearish_keywords if word in text)

                if bullish_count > bearish_count:
                    sentiments.append(0.7 + (0.1 * (bullish_count - bearish_count)))
                elif bearish_count > bullish_count:
                    sentiments.append(0.3 - (0.1 * (bearish_count - bullish_count)))
                else:
                    sentiments.append(0.5)

            # Weight recent articles more heavily
            weighted_sum = 0
            weight_sum = 0
            for i, sentiment in enumerate(sentiments):
                weight = 1 / (i + 1)  # More recent articles get higher weight
                weighted_sum += sentiment * weight
                weight_sum += weight

            return min(1.0, max(0.0, weighted_sum / weight_sum)) if weight_sum > 0 else 0.5

        except Exception as e:
            logger.error(f"Error in news sentiment analysis: {e}")
            return 0.5

    async def _analyze_social_media_sentiment(self, symbol: str) -> float:
        """Analyze social media sentiment from Twitter and Reddit"""
        try:
            sentiments = []

            # Twitter sentiment
            if self.twitter_api:
                try:
                    # Search tweets about the symbol
                    query = f"#{symbol.replace('/', '')} OR {symbol.split('/')[0]}"
                    tweets = self.twitter_api.search_tweets(q=query, lang="en", count=100)

                    # Analyze tweet sentiment (simplified)
                    for tweet in tweets:
                        text = tweet.text.lower()
                        if any(word in text for word in ['bullish', 'buy', 'long', 'moon', 'pump']):
                            sentiments.append(0.8)
                        elif any(word in text for word in ['bearish', 'sell', 'short', 'dump', 'crash']):
                            sentiments.append(0.2)
                        else:
                            sentiments.append(0.5)
                except Exception as e:
                    logger.debug(f"Twitter API error (expected if not configured): {e}")

            # Reddit sentiment
            if self.reddit_api:
                try:
                    # Search crypto subreddits
                    subreddits = ['cryptocurrency', 'cryptomarkets', 'bitcoinmarkets']
                    for subreddit in subreddits:
                        for post in self.reddit_api.subreddit(subreddit).search(symbol.split('/')[0], time_filter='day'):
                            # Analyze post sentiment (simplified)
                            text = (post.title + ' ' + post.selftext).lower()
                            if any(word in text for word in ['bullish', 'buy', 'long', 'moon', 'pump']):
                                sentiments.append(0.8)
                            elif any(word in text for word in ['bearish', 'sell', 'short', 'dump', 'crash']):
                                sentiments.append(0.2)
                            else:
                                sentiments.append(0.5)
                except Exception as e:
                    logger.debug(f"Reddit API error (expected if not configured): {e}")

            # Calculate average sentiment
            return statistics.mean(sentiments) if sentiments else 0.5

        except Exception as e:
            logger.error(f"Error in social media sentiment analysis: {e}")
            return 0.5

    async def _analyze_volume_trend(self, market_data: Dict) -> float:
        """Analyze volume trend"""
        try:
            current_volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume_24h', current_volume)

            if avg_volume == 0:
                return 0.5

            volume_ratio = current_volume / avg_volume

            # Convert to 0-1 scale
            if volume_ratio > 2:
                return 1.0
            elif volume_ratio > 1.5:
                return 0.8
            elif volume_ratio > 1:
                return 0.6
            elif volume_ratio > 0.5:
                return 0.4
            else:
                return 0.2

        except Exception:
            return 0.5

    async def _analyze_patterns_with_ai(self, market_data: Dict) -> List[Dict]:
        """Advanced pattern analysis using LangChain AI"""
        try:
            if not self.llm:
                return []

            # Prepare market data for analysis
            context = (
                f"Analyze this market data for trading patterns:\n"
                f"Current price: {market_data['price']}\n"
                f"24h High: {market_data.get('high_24h', 'unknown')}\n"
                f"24h Low: {market_data.get('low_24h', 'unknown')}\n"
                f"24h Volume: {market_data.get('volume', 'unknown')}\n"
                f"Price changes:\n"
                f"- 1h: {market_data.get('price_change_1h', 0)}%\n"
                f"- 4h: {market_data.get('price_change_4h', 0)}%\n"
                f"- 24h: {market_data.get('price_change_24h', 0)}%\n"
                f"Identify any significant patterns and rate their reliability."
            )

            # Get AI analysis
            response = self.llm.invoke([HumanMessage(content=context)])
            analysis = response.content

            # Parse AI response for patterns
            patterns = []

            # Common pattern keywords
            pattern_keywords = {
                'double bottom': {'type': 'reversal', 'signal': 'bullish'},
                'double top': {'type': 'reversal', 'signal': 'bearish'},
                'head and shoulders': {'type': 'reversal', 'signal': 'bearish'},
                'inverse head and shoulders': {'type': 'reversal', 'signal': 'bullish'},
                'bull flag': {'type': 'continuation', 'signal': 'bullish'},
                'bear flag': {'type': 'continuation', 'signal': 'bearish'},
                'triangle': {'type': 'consolidation', 'signal': 'neutral'},
                'breakout': {'type': 'breakout', 'signal': 'bullish'},
                'breakdown': {'type': 'breakdown', 'signal': 'bearish'}
            }

            # Check for patterns in AI response
            for pattern, info in pattern_keywords.items():
                if pattern.lower() in analysis.lower():
                    confidence = 0.7  # Base confidence
                    if 'strong' in analysis.lower():
                        confidence = 0.9
                    elif 'weak' in analysis.lower():
                        confidence = 0.5

                    patterns.append({
                        'type': info['type'],
                        'signal': info['signal'],
                        'confidence': confidence,
                        'ai_detected': True
                    })

            return patterns

        except Exception as e:
            logger.debug(f"AI pattern analysis not available (expected if OpenAI not configured): {e}")
            return []

    async def _analyze_volume_profile(self, market_data: Dict) -> Dict:
        """Analyze volume profile for better entry/exit points"""
        try:
            # Get price levels with significant volume
            price = market_data['price']
            volume = market_data.get('volume', 0)
            high = market_data.get('high_24h', price)
            low = market_data.get('low_24h', price)

            # Calculate price zones
            zone_count = 10
            zone_size = (high - low) / zone_count
            zones = []

            for i in range(zone_count):
                zone_low = low + (i * zone_size)
                zone_high = zone_low + zone_size
                zone_volume = volume / zone_count  # Simplified simulation

                zones.append({
                    'price_range': (zone_low, zone_high),
                    'volume': zone_volume
                })

            # Find high volume nodes (potential support/resistance)
            avg_volume = volume / zone_count
            high_volume_zones = [
                zone for zone in zones
                if zone['volume'] > avg_volume * 1.5
            ]

            # Find closest support/resistance
            support_levels = []
            resistance_levels = []

            for zone in high_volume_zones:
                zone_mid = sum(zone['price_range']) / 2
                if zone_mid < price:
                    support_levels.append(zone_mid)
                else:
                    resistance_levels.append(zone_mid)

            return {
                'value_area_high': max(z['price_range'][1] for z in zones[:int(zone_count * 0.7)]),
                'value_area_low': min(z['price_range'][0] for z in zones[:int(zone_count * 0.7)]),
                'high_volume_nodes': [sum(z['price_range']) / 2 for z in high_volume_zones],
                'nearest_support': min(support_levels) if support_levels else low,
                'nearest_resistance': min(resistance_levels) if resistance_levels else high
            }

        except Exception as e:
            logger.error(f"Error in volume profile analysis: {e}")
            return {}

    def _calculate_ai_confidence(self, sentiment: Dict, trend: Dict, patterns: List) -> float:
        """Calculate overall AI confidence"""
        try:
            confidence_factors = []

            # Sentiment confidence
            confidence_factors.append(sentiment.get('confidence', 0.0))

            # Trend confidence (based on strength)
            confidence_factors.append(trend.get('strength', 0.0))

            # Pattern confidence
            if patterns:
                pattern_confidences = [p.get('confidence', 0.0) for p in patterns]
                pattern_confidence = statistics.mean(pattern_confidences) if pattern_confidences else 0.0
                confidence_factors.append(pattern_confidence)
            else:
                confidence_factors.append(0.3)  # Lower confidence without patterns

            # Average confidence
            return statistics.mean(confidence_factors) if confidence_factors else 0.0

        except Exception:
            return 0.0

    def _generate_recommendation(self, sentiment: Dict, trend: Dict, patterns: List) -> Dict:
        """Generate enhanced AI recommendation with pattern confirmation and risk metrics"""
        try:
            # Validate signals first
            if not self._validate_signals(sentiment, trend, patterns):
                logger.warning("Signal validation failed, defaulting to hold")
                return {'action': 'hold', 'risk_metrics': None}

            sentiment_score = sentiment.get('score', 0.5)
            trend_direction = trend.get('direction', 'neutral')
            trend_strength = trend.get('strength', 0.0)

            # Get risk metrics (use dummy data if market_data not available)
            dummy_market_data = {
                'price': 100,
                'high_24h': 105,
                'low_24h': 95
            }
            risk_metrics = self._calculate_risk_metrics(dummy_market_data, patterns)

            # Analyze patterns for confirmation
            bullish_patterns = sum(1 for p in patterns if p.get('signal') == 'bullish')
            bearish_patterns = sum(1 for p in patterns if p.get('signal') == 'bearish')
            ai_patterns = sum(1 for p in patterns if p.get('ai_detected', False))

            # Calculate pattern confidence
            pattern_confidence = max(
                sum(p.get('confidence', 0) for p in patterns if p.get('signal') == 'bullish'),
                sum(p.get('confidence', 0) for p in patterns if p.get('signal') == 'bearish')
            )

            # Strong bullish signals with pattern confirmation
            if (sentiment_score > 0.7 and trend_direction == 'bullish' and
                trend_strength > 0.7 and bullish_patterns >= 2 and pattern_confidence > 1.2):
                return {
                    'action': 'strong_buy',
                    'risk_metrics': risk_metrics,
                    'confidence': pattern_confidence,
                    'pattern_count': bullish_patterns,
                    'ai_confirmation': ai_patterns >= 1
                }

            # Strong bullish signals from AI
            elif (sentiment_score > 0.7 and trend_direction == 'bullish' and
                  trend_strength > 0.6 and ai_patterns >= 1 and bullish_patterns > bearish_patterns):
                return {
                    'action': 'strong_buy',
                    'risk_metrics': risk_metrics,
                    'confidence': pattern_confidence * 0.9,  # Slightly lower confidence
                    'pattern_count': bullish_patterns,
                    'ai_confirmation': True
                }

            # Moderate bullish signals
            elif (sentiment_score > 0.6 and trend_direction == 'bullish' and
                  (bullish_patterns > 0 or pattern_confidence > 0.7)):
                return 'buy'

            # Strong bearish signals with pattern confirmation
            elif (sentiment_score < 0.3 and trend_direction == 'bearish' and
                  trend_strength > 0.7 and bearish_patterns >= 2 and pattern_confidence > 1.2):
                return 'strong_sell'

            # Strong bearish signals from AI
            elif (sentiment_score < 0.3 and trend_direction == 'bearish' and
                  trend_strength > 0.6 and ai_patterns >= 1 and bearish_patterns > bullish_patterns):
                return 'strong_sell'

            # Moderate bearish signals
            elif (sentiment_score < 0.4 and trend_direction == 'bearish' and
                  (bearish_patterns > 0 or pattern_confidence > 0.7)):
                return 'sell'

            # Default to hold
            else:
                return 'hold'

        except Exception as e:
            logger.error(f"Error generating recommendation: {e}")
            return 'hold'

    def _validate_signals(self, sentiment: Dict, trend: Dict, patterns: List) -> bool:
        """Validate AI signals before generating recommendations"""
        try:
            # Validate sentiment dictionary
            if not isinstance(sentiment, dict):
                logger.warning("Invalid sentiment type")
                return False

            # Check sentiment bounds and confidence
            if not 0 <= sentiment.get('score', 0) <= 1:
                logger.warning("Invalid sentiment score")
                return False

            if not 0 <= sentiment.get('confidence', 0) <= 1:
                logger.warning("Invalid sentiment confidence")
                return False

            # Validate trend dictionary
            if not isinstance(trend, dict):
                logger.warning("Invalid trend type")
                return False

            # Validate trend direction
            if trend.get('direction') not in ['bullish', 'bearish', 'neutral']:
                logger.warning("Invalid trend direction")
                return False

            # Validate trend strength
            if not 0 <= trend.get('strength', 0) <= 1:
                logger.warning("Invalid trend strength")
                return False

            # Validate patterns list
            if not isinstance(patterns, list):
                logger.warning("Invalid patterns type")
                return False

            # Validate pattern confidence and structure
            for pattern in patterns:
                if not isinstance(pattern, dict):
                    logger.warning("Invalid pattern structure")
                    return False

                if not 0 <= pattern.get('confidence', 0) <= 1:
                    logger.warning("Invalid pattern confidence")
                    return False

                if 'type' not in pattern or 'signal' not in pattern:
                    logger.warning("Missing pattern attributes")
                    return False

                if pattern.get('signal') not in ['bullish', 'bearish', 'neutral']:
                    logger.warning("Invalid pattern signal")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error validating signals: {e}")
            return False

    def _calculate_risk_metrics(self, market_data: Dict, patterns: List) -> Dict:
        """Calculate risk metrics for trade decisions"""
        try:
            current_price = market_data['price']
            high_24h = market_data.get('high_24h', current_price)
            low_24h = market_data.get('low_24h', current_price)

            # Calculate volatility
            price_range = (high_24h - low_24h) / low_24h * 100

            # Risk level based on volatility
            risk_level = 'high' if price_range > 10 else 'medium' if price_range > 5 else 'low'

            # Calculate optimal position size (1-5% of portfolio based on risk)
            position_size = 0.05  # Default 5%
            if risk_level == 'high':
                position_size = 0.01  # 1% for high risk
            elif risk_level == 'medium':
                position_size = 0.03  # 3% for medium risk

            # Dynamic stop loss based on volatility and patterns
            stop_loss_pct = max(price_range * 0.5, 2.0)  # Minimum 2%

            # Take profit targets based on risk:reward
            take_profit_1 = stop_loss_pct * 1.5  # 1.5:1 ratio
            take_profit_2 = stop_loss_pct * 2.0  # 2:1 ratio
            take_profit_3 = stop_loss_pct * 3.0  # 3:1 ratio

            return {
                'risk_level': risk_level,
                'volatility': price_range,
                'position_size': position_size,
                'stop_loss_pct': stop_loss_pct,
                'take_profit_levels': [take_profit_1, take_profit_2, take_profit_3]
            }

        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return {
                'risk_level': 'high',
                'volatility': 0,
                'position_size': 0.01,
                'stop_loss_pct': 2.0,
                'take_profit_levels': [3.0, 4.0, 6.0]
            }
