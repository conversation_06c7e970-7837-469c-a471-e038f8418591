from typing import Dict
import logging

logger = logging.getLogger(__name__)

# Technical analysis class
class TechnicalAnalyzer:
    async def analyze(self, symbol: str, market_data: Dict, timeframe: str = "1h") -> Dict:
        """Perform technical analysis with consistency checks"""
        try:
            # Validate input data
            if not self._validate_input_data(market_data):
                return {"error": "Invalid input data"}
                
            # Calculate indicators with bounds checking
            indicators = {}
            
            # RSI bounds check
            rsi = self._calculate_rsi(market_data['close'])
            if 0 <= rsi <= 100:
                indicators['rsi'] = rsi
            else:
                logger.warning(f"Invalid RSI value: {rsi}")
                
            # Moving average validation
            sma = self._calculate_sma(market_data['close'], 20)
            if sma > 0:
                indicators['sma_20'] = sma
                
            return indicators
            
        except Exception as e:
            logger.error(f"Error in technical analysis: {e}")
            return {"error": str(e)}

# AI analysis class
class AIAnalyzer:
    async def analyze_market(self, symbol: str, market_data: Dict) -> Dict:
        """Analyze market with fallback options"""
        try:
            # Primary analysis path
            analysis = await self._perform_primary_analysis(symbol, market_data)
            if analysis:
                return analysis
                
            # Fallback to simplified analysis
            logger.warning("Primary analysis failed, using fallback")
            return await self._perform_fallback_analysis(symbol, market_data)
            
        except Exception as e:
            logger.error(f"Analysis error: {e}")
            # Return safe default values
            return {
                'sentiment': {'score': 0.5, 'confidence': 0.0},
                'recommendation': 'hold',
                'confidence': 0.0
            }