"""
Simple test to check basic functionality
"""
import asyncio
from config import get_settings

async def test_config():
    """Test configuration loading"""
    print("🧪 Testing configuration...")
    
    settings = get_settings()
    
    print(f"✅ Telegram Bot Token: {'✓' if settings.telegram_bot_token else '❌'}")
    print(f"✅ Telegram Admin ID: {'✓' if settings.telegram_admin_user_id else '❌'}")
    print(f"✅ KuCoin API Key: {'✓' if settings.kucoin_api_key else '❌'}")
    print(f"✅ MEXC API Key: {'✓' if settings.mexc_api_key else '❌'}")
    
    is_valid = settings.validate()
    print(f"\n📋 Configuration: {'✅ Valid' if is_valid else '❌ Invalid'}")
    
    return is_valid

async def test_imports():
    """Test if all modules can be imported"""
    print("\n🧪 Testing imports...")
    
    try:
        from exchanges.manager import ExchangeManager
        print("✅ Exchange Manager")
        
        from strategies.manager import StrategyManager
        print("✅ Strategy Manager")
        
        from analysis.market_analyzer import MarketAnalyzer
        print("✅ Market Analyzer")
        
        from bot.handlers import TradingBotHandlers
        print("✅ Bot Handlers")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Simple Trading Bot Test")
    print("=" * 40)
    
    # Test configuration
    config_ok = await test_config()
    
    # Test imports
    imports_ok = await test_imports()
    
    print("\n" + "=" * 40)
    if config_ok and imports_ok:
        print("🎉 Basic tests passed!")
        print("\nNext steps:")
        print("1. Check your .env file with real API keys")
        print("2. Run: python main.py")
    else:
        print("❌ Some tests failed. Check the errors above.")

if __name__ == "__main__":
    asyncio.run(main())
