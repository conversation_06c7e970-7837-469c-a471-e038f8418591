#!/usr/bin/env python3
"""
Test authorization functionality
"""
from config import get_settings

def test_authorization():
    """Test the authorization system"""
    print("🔐 Testing Authorization System")
    print("=" * 40)
    
    settings = get_settings()
    
    print(f"📋 Configured Admin User IDs: {settings.telegram_admin_user_ids}")
    print(f"📋 Primary Admin ID (backward compatibility): {settings.telegram_admin_user_id}")
    
    # Test cases
    test_cases = [
        6229184945,  # Original admin ID
        7926586899,  # New admin ID
        123456789,   # Unauthorized ID
        0,           # Invalid ID
    ]
    
    print("\n🧪 Authorization Tests:")
    for user_id in test_cases:
        is_authorized = settings.is_authorized_user(user_id)
        status = "✅ AUTHORIZED" if is_authorized else "❌ NOT AUTHORIZED"
        print(f"  User ID {user_id}: {status}")
    
    print("\n📱 Telegram Bot Test:")
    from telegram_simple import SimpleTelegramBot
    
    bot = SimpleTelegramBot()
    
    for user_id in test_cases:
        is_authorized = bot.is_authorized(user_id)
        status = "✅ AUTHORIZED" if is_authorized else "❌ NOT AUTHORIZED"
        print(f"  Bot check for {user_id}: {status}")
    
    print("\n" + "=" * 40)
    print("🎯 Summary:")
    print(f"✅ User ID 6229184945 should be authorized")
    print(f"✅ User ID 7926586899 should be authorized")
    print(f"❌ Other user IDs should NOT be authorized")

if __name__ == "__main__":
    test_authorization()
