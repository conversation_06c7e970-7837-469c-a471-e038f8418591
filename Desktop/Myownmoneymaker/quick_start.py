#!/usr/bin/env python3
"""
Quick Start - Eenvoudige manier om de trading bot te starten
"""
import sys
import os

def print_header():
    """Print header"""
    print("🤖 Trading Bot Quick Start")
    print("=" * 30)
    print()

def check_python():
    """Check Python version"""
    print("🐍 Python Check:")
    print(f"   Version: {sys.version}")
    
    if sys.version_info >= (3, 8):
        print("   ✅ Python version OK")
        return True
    else:
        print("   ❌ Python 3.8+ required")
        return False

def check_files():
    """Check if required files exist"""
    print("\n📁 File Check:")
    
    required_files = [
        'config.py',
        '.env',
        'simple_bot.py',
        'telegram_simple.py'
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file} - MISSING")
            all_exist = False
    
    return all_exist

def install_packages():
    """Install required packages"""
    print("\n📦 Installing packages...")
    
    packages = [
        'ccxt',
        'loguru', 
        'python-dotenv',
        'aiohttp'
    ]
    
    import subprocess
    
    for package in packages:
        try:
            print(f"   Installing {package}...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', package
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"   ✅ {package}")
        except subprocess.CalledProcessError:
            print(f"   ❌ {package} - FAILED")

def test_imports():
    """Test if imports work"""
    print("\n🧪 Import Test:")
    
    try:
        import ccxt
        print("   ✅ ccxt")
    except ImportError:
        print("   ❌ ccxt")
        return False
    
    try:
        from loguru import logger
        print("   ✅ loguru")
    except ImportError:
        print("   ❌ loguru")
        return False
    
    try:
        from dotenv import load_dotenv
        print("   ✅ python-dotenv")
    except ImportError:
        print("   ❌ python-dotenv")
        return False
    
    try:
        import aiohttp
        print("   ✅ aiohttp")
    except ImportError:
        print("   ❌ aiohttp")
        return False
    
    return True

def show_instructions():
    """Show startup instructions"""
    print("\n🚀 Ready to Start!")
    print("=" * 20)
    print()
    print("Choose one of these methods:")
    print()
    print("📱 TELEGRAM BOT (Recommended):")
    print("   python telegram_simple.py")
    print("   Then go to @mynewmoneymakersbot and type /start")
    print()
    print("💻 CONSOLE BOT:")
    print("   python simple_bot.py")
    print("   Use commands like: balance, price BTC/USDT, start")
    print()
    print("🧪 TEST ONLY:")
    print("   python test_exchanges.py")
    print("   Check if exchanges connect")
    print()
    print("🔧 DEBUG:")
    print("   python debug_bot.py")
    print("   Detailed error checking")
    print()

def run_bot_choice():
    """Let user choose and run bot"""
    print("🎯 Quick Start Options:")
    print("1. Start Telegram Bot")
    print("2. Start Console Bot") 
    print("3. Test Exchanges")
    print("4. Show Instructions Only")
    print()
    
    choice = input("Choose (1-4): ").strip()
    
    if choice == "1":
        print("\n🚀 Starting Telegram Bot...")
        os.system(f"{sys.executable} telegram_simple.py")
    elif choice == "2":
        print("\n🚀 Starting Console Bot...")
        os.system(f"{sys.executable} simple_bot.py")
    elif choice == "3":
        print("\n🧪 Testing Exchanges...")
        os.system(f"{sys.executable} test_exchanges.py")
    elif choice == "4":
        show_instructions()
    else:
        print("❌ Invalid choice")
        show_instructions()

def main():
    """Main function"""
    print_header()
    
    # Check Python
    if not check_python():
        input("Press Enter to exit...")
        return
    
    # Check files
    if not check_files():
        print("\n❌ Missing files. Make sure you're in the right directory.")
        input("Press Enter to exit...")
        return
    
    # Install packages
    install_packages()
    
    # Test imports
    if not test_imports():
        print("\n❌ Import test failed. Try installing packages manually:")
        print("pip install ccxt loguru python-dotenv aiohttp")
        input("Press Enter to exit...")
        return
    
    print("\n✅ All checks passed!")
    print()
    
    # Run bot
    run_bot_choice()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        print("\nTry running the individual scripts:")
        print("python simple_bot.py")
        print("python telegram_simple.py")
        input("Press Enter to exit...")
