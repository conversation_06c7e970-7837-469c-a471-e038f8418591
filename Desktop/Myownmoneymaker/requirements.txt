# Telegram Bot
python-telegram-bot>=20.0,<21.0

# Exchange APIs
ccxt==4.2.25

# Environment variables
python-dotenv>=1.0.0

# Async support
aiohttp==3.9.1

# Logging
loguru==0.7.2

# Data Analysis & Technical Indicators
pandas>=2.0.0
pandas_ta>=0.3.14b

# Configuration (disabled for Python 3.13 compatibility)
# pydantic==2.5.2
# pydantic-settings==2.1.0

# Security
cryptography>=41.0.0

# Testing (optional)
# pytest==7.4.3
# pytest-asyncio==0.21.1

# Additional Dependencies
tweepy>=4.14.0
praw>=7.7.1
newsapi-python>=0.2.7
