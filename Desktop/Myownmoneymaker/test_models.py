import openai
import time
from typing import List, Dict
import json
from loguru import logger

# Configuratie voor LM Studio
openai.api_base = "http://127.0.0.1:1234/v1"
openai.api_key = "lm-studio"

# Beschikbare modellen in LM Studio
MODELS = [
    "deepseek-coder-33b-instruct",
    "lmstudio-communitymodels",
    "mistralai/mistra",
    "microsoft/phi-4-mini-reasoning",
    "qwen/qwen3-8b-2"
]

# Test prompts specifiek voor trading
TEST_PROMPTS = [
    {
        "name": "Technical Analysis",
        "prompt": "Explain how to implement RSI (Relative Strength Index) in Python for crypto trading."
    },
    {
        "name": "Trading Strategy",
        "prompt": "Write a basic trading strategy that uses moving averages to generate buy/sell signals."
    },
    {
        "name": "Error Handling",
        "prompt": "How would you implement proper error handling for API calls to a crypto exchange?"
    },
    {
        "name": "Data Processing",
        "prompt": "Write a function to calculate the volatility of a crypto asset using its price history."
    }
]

def test_model(model: str, prompt: Dict) -> Dict:
    """Test een specifiek model met een prompt en meet de prestaties"""
    try:
        start_time = time.time()
        
        response = openai.ChatCompletion.create(
            model=model,
            messages=[
                {"role": "system", "content": "Je bent een expert in cryptocurrency trading en Python programming."},
                {"role": "user", "content": prompt["prompt"]}
            ],
            temperature=0.2,
            max_tokens=500
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        return {
            "model": model,
            "prompt_name": prompt["name"],
            "success": True,
            "response_time": response_time,
            "response_length": len(response.choices[0].message["content"]),
            "response": response.choices[0].message["content"]
        }
    except Exception as e:
        logger.error(f"Error testing {model} with {prompt['name']}: {str(e)}")
        return {
            "model": model,
            "prompt_name": prompt["name"],
            "success": False,
            "error": str(e)
        }

def evaluate_models():
    """Test alle modellen en genereer een rapport"""
    results = []
    
    logger.info("🚀 Starting model evaluation...")
    
    for model in MODELS:
        logger.info(f"Testing model: {model}")
        model_results = []
        
        for prompt in TEST_PROMPTS:
            result = test_model(model, prompt)
            model_results.append(result)
            
            if result["success"]:
                logger.info(f"✅ {prompt['name']}: {result['response_time']:.2f}s")
            else:
                logger.error(f"❌ {prompt['name']}: Failed")
        
        results.extend(model_results)
    
    return results

def generate_report(results: List[Dict]):
    """Genereer een samenvattend rapport van de modelresultaten"""
    report = "📊 Model Evaluation Report\n\n"
    
    # Bereken statistieken per model
    model_stats = {}
    for result in results:
        model = result["model"]
        if model not in model_stats:
            model_stats[model] = {
                "success_count": 0,
                "total_count": 0,
                "total_time": 0,
                "avg_response_length": 0
            }
        
        model_stats[model]["total_count"] += 1
        if result["success"]:
            model_stats[model]["success_count"] += 1
            model_stats[model]["total_time"] += result["response_time"]
            model_stats[model]["avg_response_length"] += result["response_length"]
    
    # Genereer rapport per model
    for model, stats in model_stats.items():
        success_rate = (stats["success_count"] / stats["total_count"]) * 100
        avg_time = stats["total_time"] / stats["success_count"] if stats["success_count"] > 0 else 0
        avg_length = stats["avg_response_length"] / stats["success_count"] if stats["success_count"] > 0 else 0
        
        report += f"Model: {model}\n"
        report += f"Success Rate: {success_rate:.1f}%\n"
        report += f"Average Response Time: {avg_time:.2f}s\n"
        report += f"Average Response Length: {avg_length:.0f} chars\n\n"
    
    # Aanbeveling
    best_model = max(model_stats.items(), key=lambda x: x[1]["success_count"])
    report += "🎯 Recommendation:\n"
    report += f"Based on the tests, {best_model[0]} performs best for trading bot development.\n"
    
    return report

if __name__ == "__main__":
    logger.info("🤖 Starting model evaluation for trading bot development...")
    results = evaluate_models()
    
    # Sla ruwe resultaten op
    with open("model_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    # Genereer en toon rapport
    report = generate_report(results)
    print("\n" + report)
    
    # Sla rapport op
    with open("model_evaluation_report.txt", "w") as f:
        f.write(report)
    
    logger.info("✅ Evaluation complete! Check model_evaluation_report.txt for details.")
