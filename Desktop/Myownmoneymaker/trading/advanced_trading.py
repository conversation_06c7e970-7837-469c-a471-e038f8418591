"""
Advanced Trading Module
Handles spot to futures conversion, coin conversion, and advanced trading features
"""

import asyncio
from decimal import Decimal
from typing import Dict, List, Optional, Tuple
from loguru import logger

from exchanges.manager import ExchangeManager
from exchanges.base import Balance


class AdvancedTradingManager:
    """Manages advanced trading operations"""

    def __init__(self, exchange_manager: ExchangeManager):
        self.exchange_manager = exchange_manager

    async def convert_spot_to_futures(self, symbol: str, amount: Decimal, exchange: str = "kucoin") -> Dict:
        """
        Convert spot position to futures position

        Args:
            symbol: Trading pair (e.g., 'BTC/USDT')
            amount: Amount to convert
            exchange: Exchange to use

        Returns:
            Dict with conversion result
        """
        try:
            logger.info(f"🔄 Converting {amount} {symbol} from spot to futures on {exchange}")

            # Step 1: Check spot balance
            balances = await self.exchange_manager.get_balance(exchange)
            base_currency = symbol.split('/')[0]

            if base_currency not in balances or balances[base_currency].free < amount:
                return {
                    'success': False,
                    'error': f'Insufficient {base_currency} balance for conversion',
                    'required': float(amount),
                    'available': float(balances.get(base_currency, Balance(base_currency, 0, 0, 0)).free)
                }

            # Step 2: Transfer from spot to futures account (if supported)
            transfer_result = await self._transfer_to_futures_account(exchange, base_currency, amount)

            if not transfer_result['success']:
                return transfer_result

            # Step 3: Open futures position
            futures_result = await self._open_futures_position(exchange, symbol, amount)

            return {
                'success': True,
                'spot_to_futures_transfer': transfer_result,
                'futures_position': futures_result,
                'message': f'Successfully converted {amount} {base_currency} to futures position'
            }

        except Exception as e:
            logger.error(f"Error converting spot to futures: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def convert_coin(self, from_currency: str, to_currency: str, amount: Decimal, exchange: str = "kucoin") -> Dict:
        """
        Convert one cryptocurrency to another

        Args:
            from_currency: Source currency (e.g., 'BTC')
            to_currency: Target currency (e.g., 'ETH')
            amount: Amount to convert
            exchange: Exchange to use

        Returns:
            Dict with conversion result
        """
        try:
            logger.info(f"💱 Converting {amount} {from_currency} to {to_currency} on {exchange}")

            # Step 1: Check balance
            balances = await self.exchange_manager.get_balance(exchange)

            if from_currency not in balances or balances[from_currency].free < amount:
                return {
                    'success': False,
                    'error': f'Insufficient {from_currency} balance',
                    'required': float(amount),
                    'available': float(balances.get(from_currency, Balance(from_currency, 0, 0, 0)).free)
                }

            # Step 2: Find best conversion path
            conversion_path = await self._find_conversion_path(from_currency, to_currency, exchange)

            if not conversion_path:
                return {
                    'success': False,
                    'error': f'No conversion path found from {from_currency} to {to_currency}'
                }

            # Step 3: Execute conversion
            conversion_result = await self._execute_conversion(conversion_path, amount, exchange)

            return conversion_result

        except Exception as e:
            logger.error(f"Error converting {from_currency} to {to_currency}: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def get_futures_positions(self, exchange: str = "kucoin") -> List[Dict]:
        """Get all open futures positions"""
        try:
            # This would need to be implemented based on exchange API
            # For now, return empty list
            logger.info(f"📊 Getting futures positions from {exchange}")
            return []

        except Exception as e:
            logger.error(f"Error getting futures positions: {e}")
            return []

    async def close_futures_position(self, symbol: str, exchange: str = "kucoin") -> Dict:
        """Close a futures position"""
        try:
            logger.info(f"🔒 Closing futures position for {symbol} on {exchange}")

            # This would need to be implemented based on exchange API
            return {
                'success': True,
                'message': f'Futures position for {symbol} closed successfully'
            }

        except Exception as e:
            logger.error(f"Error closing futures position: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _transfer_to_futures_account(self, exchange: str, currency: str, amount: Decimal) -> Dict:
        """Transfer funds from spot to futures account"""
        try:
            # This is exchange-specific implementation
            # For KuCoin, this would use their internal transfer API
            logger.info(f"📤 Transferring {amount} {currency} to futures account on {exchange}")

            # Simulate successful transfer for now
            return {
                'success': True,
                'transfer_id': f'transfer_{currency}_{amount}',
                'message': f'Transferred {amount} {currency} to futures account'
            }

        except Exception as e:
            logger.error(f"Error transferring to futures account: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _open_futures_position(self, exchange: str, symbol: str, amount: Decimal) -> Dict:
        """Open a futures position"""
        try:
            logger.info(f"📈 Opening futures position for {symbol} with {amount}")

            # This would create a futures order
            # For now, simulate successful position opening
            return {
                'success': True,
                'position_id': f'futures_{symbol}_{amount}',
                'symbol': symbol,
                'amount': float(amount),
                'leverage': 1,  # Default leverage
                'message': f'Futures position opened for {symbol}'
            }

        except Exception as e:
            logger.error(f"Error opening futures position: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _find_conversion_path(self, from_currency: str, to_currency: str, exchange: str) -> Optional[List[str]]:
        """Find the best path to convert between currencies"""
        try:
            # Common conversion paths
            common_pairs = ['USDT', 'BTC', 'ETH', 'BNB']

            # Direct conversion
            direct_symbol = f"{from_currency}/{to_currency}"
            reverse_symbol = f"{to_currency}/{from_currency}"

            # Check if direct conversion is possible
            try:
                await self.exchange_manager.get_ticker_from_all(direct_symbol)
                return [direct_symbol]
            except:
                pass

            try:
                await self.exchange_manager.get_ticker_from_all(reverse_symbol)
                return [reverse_symbol]
            except:
                pass

            # Try conversion through common pairs
            for intermediate in common_pairs:
                if intermediate == from_currency or intermediate == to_currency:
                    continue

                try:
                    path1 = f"{from_currency}/{intermediate}"
                    path2 = f"{intermediate}/{to_currency}"

                    # Check if both pairs exist
                    await self.exchange_manager.get_ticker_from_all(path1)
                    await self.exchange_manager.get_ticker_from_all(path2)

                    return [path1, path2]
                except:
                    continue

            return None

        except Exception as e:
            logger.error(f"Error finding conversion path: {e}")
            return None

    async def _execute_conversion(self, conversion_path: List[str], amount: Decimal, exchange: str) -> Dict:
        """Execute the conversion through the found path"""
        try:
            logger.info(f"🔄 Executing conversion path: {' -> '.join(conversion_path)}")

            current_amount = amount
            orders = []

            for i, symbol in enumerate(conversion_path):
                # Determine if we need to buy or sell
                base, quote = symbol.split('/')

                if i == 0:
                    # First conversion: sell the original currency
                    side = "sell"
                    order_amount = current_amount
                else:
                    # Subsequent conversions: buy the target currency
                    side = "buy"
                    # Calculate amount based on previous conversion
                    ticker = await self.exchange_manager.get_ticker_from_all(symbol)
                    if ticker:
                        price = list(ticker.values())[0].last
                        order_amount = current_amount / price

                # Create market order
                order = await self.exchange_manager.create_order(
                    exchange_name=exchange,
                    order_type="market",
                    side=side,
                    symbol=symbol,
                    amount=order_amount
                )

                if order:
                    orders.append({
                        'symbol': symbol,
                        'side': side,
                        'amount': float(order_amount),
                        'order_id': order.id,
                        'status': order.status
                    })

                    # Update current amount for next conversion
                    if side == "sell":
                        current_amount = order.filled * Decimal(str(order.price))
                    else:
                        current_amount = order.filled
                else:
                    return {
                        'success': False,
                        'error': f'Failed to create order for {symbol}',
                        'completed_orders': orders
                    }

            return {
                'success': True,
                'conversion_path': conversion_path,
                'orders': orders,
                'final_amount': float(current_amount),
                'message': f'Conversion completed successfully'
            }

        except Exception as e:
            logger.error(f"Error executing conversion: {e}")
            return {
                'success': False,
                'error': str(e)
            }
