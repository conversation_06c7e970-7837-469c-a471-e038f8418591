"""
Test Telegram bot basic functionality
"""
import asyncio
from telegram.ext import Application, CommandHandler
from config import get_settings

async def start_command(update, context):
    """Simple start command"""
    await update.message.reply_text("🤖 <PERSON><PERSON> is working!")

async def test_telegram_bot():
    """Test basic Telegram bot functionality"""
    print("🧪 Testing Telegram bot...")
    
    try:
        settings = get_settings()
        
        if not settings.telegram_bot_token:
            print("❌ No Telegram bot token found in .env file")
            print("Please add your bot token to .env file:")
            print("TELEGRAM_BOT_TOKEN=your_bot_token_here")
            return False
        
        print(f"✅ Bot token found: {settings.telegram_bot_token[:10]}...")
        
        # Create simple application
        application = Application.builder().token(settings.telegram_bot_token).build()
        
        # Add simple handler
        application.add_handler(CommandHandler("start", start_command))
        
        print("✅ Application created successfully")
        print("🚀 Starting bot polling for 10 seconds...")
        print("💬 Send /start to your bot to test!")
        
        # Start bot for 10 seconds
        async def run_for_limited_time():
            await application.initialize()
            await application.start()
            await application.updater.start_polling()
            
            # Wait 10 seconds
            await asyncio.sleep(10)
            
            await application.updater.stop()
            await application.stop()
            await application.shutdown()
        
        await run_for_limited_time()
        
        print("✅ Bot test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Telegram bot test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Telegram Bot Test")
    print("=" * 40)
    
    success = await test_telegram_bot()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 Telegram bot is working!")
        print("\nYou can now run the full trading bot:")
        print("python main.py")
    else:
        print("❌ Telegram bot test failed.")
        print("\nPlease check:")
        print("1. Your bot token in .env file")
        print("2. Internet connection")

if __name__ == "__main__":
    asyncio.run(main())
