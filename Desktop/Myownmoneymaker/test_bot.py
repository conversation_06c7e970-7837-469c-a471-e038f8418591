"""
Test script for the Telegram Trading Bot
"""
import asyncio
import sys
from decimal import Decimal
from loguru import logger
from config import get_settings
from exchanges.manager import ExchangeManager

async def test_exchange_connections():
    """Test exchange connections"""
    print("🔄 Testing exchange connections...")
    
    exchange_manager = ExchangeManager()
    results = await exchange_manager.connect_all()
    
    for exchange, connected in results.items():
        status = "✅ Connected" if connected else "❌ Failed"
        print(f"  {exchange}: {status}")
    
    return any(results.values())

async def test_balance_fetching(exchange_manager):
    """Test balance fetching"""
    print("\n🔄 Testing balance fetching...")
    
    try:
        balances = await exchange_manager.get_all_balances()
        
        for exchange, balance_dict in balances.items():
            print(f"  {exchange}:")
            if balance_dict:
                non_zero = {k: v for k, v in balance_dict.items() if v.total > 0}
                if non_zero:
                    for currency, balance in list(non_zero.items())[:3]:  # Show first 3
                        print(f"    {currency}: {balance.total}")
                else:
                    print("    No non-zero balances")
            else:
                print("    No balances retrieved")
        
        return True
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

async def test_price_fetching(exchange_manager):
    """Test price fetching"""
    print("\n🔄 Testing price fetching...")
    
    test_symbols = ["BTC/USDT", "ETH/USDT"]
    
    for symbol in test_symbols:
        print(f"  Testing {symbol}:")
        try:
            tickers = await exchange_manager.get_ticker_from_all(symbol)
            
            for exchange, ticker in tickers.items():
                print(f"    {exchange}: ${ticker.last:.2f}")
            
        except Exception as e:
            print(f"    ❌ Error for {symbol}: {e}")

async def test_best_price(exchange_manager):
    """Test best price finding"""
    print("\n🔄 Testing best price finding...")
    
    try:
        result = await exchange_manager.find_best_price("BTC/USDT", "buy")
        if result:
            exchange, price = result
            print(f"  Best buy price: {exchange} at ${price:.2f}")
        else:
            print("  No price data found")
            
        result = await exchange_manager.find_best_price("BTC/USDT", "sell")
        if result:
            exchange, price = result
            print(f"  Best sell price: {exchange} at ${price:.2f}")
        else:
            print("  No price data found")
            
    except Exception as e:
        print(f"  ❌ Error: {e}")

async def test_configuration():
    """Test configuration loading"""
    print("🔄 Testing configuration...")
    
    try:
        settings = get_settings()
        
        # Check required settings (without revealing actual values)
        checks = [
            ("Telegram Bot Token", bool(settings.telegram_bot_token)),
            ("Telegram Admin User ID", bool(settings.telegram_admin_user_id)),
            ("KuCoin API Key", bool(settings.kucoin_api_key)),
            ("KuCoin Secret", bool(settings.kucoin_secret_key)),
            ("KuCoin Passphrase", bool(settings.kucoin_passphrase)),
            ("MEXC API Key", bool(settings.mexc_api_key)),
            ("MEXC Secret", bool(settings.mexc_secret_key)),
        ]
        
        for name, configured in checks:
            status = "✅ Configured" if configured else "❌ Missing"
            print(f"  {name}: {status}")
        
        return all(configured for _, configured in checks)
        
    except Exception as e:
        print(f"  ❌ Configuration error: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 Testing Telegram Trading Bot")
    print("=" * 50)
    
    # Setup logging
    logger.remove()
    logger.add(sys.stderr, level="INFO")
    
    # Test configuration
    config_ok = await test_configuration()
    if not config_ok:
        print("\n❌ Configuration test failed. Please check your .env file.")
        return
    
    # Test exchange connections
    exchange_manager = ExchangeManager()
    connections_ok = await test_exchange_connections()
    
    if not connections_ok:
        print("\n❌ No exchange connections successful.")
        print("Please check your API credentials in .env file.")
        return
    
    # Test balance fetching
    await test_balance_fetching(exchange_manager)
    
    # Test price fetching
    await test_price_fetching(exchange_manager)
    
    # Test best price finding
    await test_best_price(exchange_manager)
    
    print("\n" + "=" * 50)
    print("🎉 All tests completed!")
    print("\nIf all tests passed, you can now run the bot with:")
    print("python main.py")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test error: {e}")
        sys.exit(1)
