#!/usr/bin/env python3
"""
Snelle test van exchange connecties
"""
import asyncio
import sys
import os

# Voeg de huidige directory toe aan het Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import directly from the root config.py file
import importlib.util
spec = importlib.util.spec_from_file_location("root_config", "./config.py")
root_config = importlib.util.module_from_spec(spec)
spec.loader.exec_module(root_config)

from exchanges.manager import ExchangeManager

async def test_exchanges():
    """Test exchange connecties"""
    print("🔍 Exchange Connectie Test")
    print("=" * 50)

    try:
        # Initialize exchange manager
        exchange_manager = ExchangeManager()

        # Test connecties
        print("📡 Verbinden met exchanges...")
        connection_results = await exchange_manager.connect_all()

        print("\n🏦 Connectie Resultaten:")
        for exchange, connected in connection_results.items():
            status = "✅ Verbonden" if connected else "❌ Verbinding mislukt"
            print(f"  {exchange.upper()}: {status}")

        # Test basis functionaliteit als verbonden
        if any(connection_results.values()):
            print("\n💰 Test basis functionaliteit:")

            # Test balances
            try:
                for exchange_name, connected in connection_results.items():
                    if connected:
                        exchange = exchange_manager.get_exchange(exchange_name)
                        if exchange:
                            balance = await exchange.get_balance()
                            print(f"  {exchange_name.upper()} balans: ✅ Opgehaald")
                        else:
                            print(f"  {exchange_name.upper()} balans: ❌ Exchange niet beschikbaar")
            except Exception as e:
                print(f"  Balans test: ❌ Fout - {e}")

            # Test market data
            try:
                for exchange_name, connected in connection_results.items():
                    if connected:
                        exchange = exchange_manager.get_exchange(exchange_name)
                        if exchange:
                            ticker = await exchange.get_ticker("BTC/USDT")
                            if ticker:
                                # ticker is een ccxt Ticker object, gebruik last attribuut
                                price = getattr(ticker, 'last', None) or getattr(ticker, 'close', 'N/A')
                                print(f"  {exchange_name.upper()} BTC/USDT prijs: ✅ ${price}")
                            else:
                                print(f"  {exchange_name.upper()} BTC/USDT prijs: ❌ Geen data")
                        else:
                            print(f"  {exchange_name.upper()} ticker: ❌ Exchange niet beschikbaar")
            except Exception as e:
                print(f"  Ticker test: ❌ Fout - {e}")

        # Cleanup (disconnect_all method might not exist, skip for now)
        # await exchange_manager.disconnect_all()

        success = any(connection_results.values())
        return success

    except Exception as e:
        print(f"❌ Fout bij het testen van exchanges: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_exchanges())
    if success:
        print("\n🎉 Exchange tests geslaagd!")
        sys.exit(0)
    else:
        print("\n💥 Er zijn problemen met de exchange connecties!")
        sys.exit(1)
