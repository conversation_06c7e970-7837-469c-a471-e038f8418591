# 🤖 AI Trading Bot

Een geavanceerde cryptocurrency trading bot met AI-gestuurde strategieën voor KuCoin en MEXC exchanges.

## 🚀 Snelle Start

### **Optie 1: Automatische Setup (Aanbevolen)**

**Windows:**

```bash
start.bat
```

**Mac/Linux:**

```bash
chmod +x start.sh
./start.sh
```

**Python:**

```bash
python start_bot.py
```

### **Optie 2: Handmatige Start**

**Console <PERSON> (Terminal interface):**

```bash
python simple_bot.py
```

**Telegram <PERSON> (Mobile interface):**

```bash
python telegram_simple.py
```

**Test Verbindingen:**

```bash
python test_exchanges.py
```

## ⚙️ Configuratie

1. **Je .env file is al geconfigureerd**
2. **API Keys zijn ingesteld voor:**
   - ✅ Telegram <PERSON> (@mynewmoneymakersbot)
   - ✅ Ku<PERSON>oin API credentials
   - ✅ MEXC API credentials

## 📱 Bot Interfaces

### **Console <PERSON>**

- Terminal-based interface
- Commands: `balance`, `price BTC/USDT`, `analysis`, `start`, `quit`
- Perfect voor testing en monitoring

### **Telegram Bot**

- Visuele knoppen interface
- Real-time notifications
- Mobile-friendly
- Commands: `/start` voor hoofdmenu

## Features

### 🤖 Automatische Trading Strategieën

- **Day Trading**: AI-powered intraday momentum trading
- **Scalping**: High-frequency korte termijn trading (1-5 min)
- **Momentum Breakout**: Trend following met breakout detectie
- **Mean Reversion**: Trading van oversold/overbought condities

### 📊 AI-Powered Marktanalyse

- Automatische marktanalyse elke 5 minuten
- Technische analyse met 20+ indicatoren
- AI sentiment analyse
- Pattern recognition
- Volume en volatiliteit analyse
- Real-time alerts en waarschuwingen

### 💰 Geavanceerd Risk Management

- Automatische stop-loss orders
- Trailing stops
- Dynamic position sizing
- Portfolio risk management
- Multi-timeframe analyse

### 📱 Telegram Interface

- Eenvoudige bediening via chat commands
- Real-time notificaties
- Portfolio overzicht
- Order management
- Strategy monitoring

### 🔄 Multi-Exchange Support

- KuCoin en MEXC exchanges
- Arbitrage detectie
- Best price execution
- Cross-exchange portfolio management

### 🔐 Security & Reliability

- Veilige API key opslag
- User authentication
- Comprehensive logging
- Error handling en recovery

## Setup

### 1. Virtual Environment

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# of
venv\Scripts\activate     # Windows
```

### 2. Dependencies

```bash
pip install -r requirements.txt
```

### 3. Configuration

1. Kopieer `.env.example` naar `.env`
2. Vul je API keys en tokens in
3. Configureer je Telegram bot token

### 4. Telegram Bot Setup

1. Ga naar [@BotFather](https://t.me/botfather) op Telegram
2. Maak een nieuwe bot met `/newbot`
3. Kopieer de bot token naar je `.env` file
4. Vind je Telegram User ID en voeg toe aan `.env`

### 5. Exchange API Setup

#### KuCoin

1. Ga naar KuCoin API Management
2. Maak nieuwe API key met trading permissions
3. Voeg credentials toe aan `.env`

#### MEXC

1. Ga naar MEXC API Management
2. Maak nieuwe API key met trading permissions
3. Voeg credentials toe aan `.env`

## Usage

```bash
python main.py
```

## Commands

### 📊 Basis Commands

- `/start` - Start de bot
- `/help` - Toon alle commands
- `/balance` - Toon portfolio balances
- `/price <symbol>` - Toon prijs van een coin (bijv. `/price BTC/USDT`)
- `/exchanges` - Toon beschikbare exchanges
- `/bestprice <symbol> <buy|sell>` - Vind beste prijs

### 🛒 Manual Trading

- `/buy <exchange> <symbol> <amount> [price]` - Koop order plaatsen
- `/sell <exchange> <symbol> <amount> [price]` - Verkoop order plaatsen
- `/orders` - Toon open orders
- `/cancel <exchange> <order_id> <symbol>` - Annuleer order

### 🤖 Automatische Trading

- `/daytrade <bedrag>` - Start day trading met opgegeven bedrag
- `/starttrading` - Start alle automatische strategieën
- `/stoptrading` - Stop automatische trading
- `/strategies` - Toon status van alle strategieën
- `/positions` - Toon actieve posities

### 📈 Marktanalyse

- `/analysis` - Toon laatste marktanalyse rapport (elke 5 min bijgewerkt)

## Security

- API keys worden versleuteld opgeslagen
- Alleen geautoriseerde gebruikers kunnen de bot gebruiken
- Alle trading acties worden gelogd

## Disclaimer

⚠️ **Risico Waarschuwing**: Trading in cryptocurrencies is risicovol. Gebruik deze bot op eigen risico.
