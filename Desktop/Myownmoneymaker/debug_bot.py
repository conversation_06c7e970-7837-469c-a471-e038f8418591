#!/usr/bin/env python3
"""
Debug script om problemen met de trading bot te identificeren
"""
import sys
import os
import traceback

def test_python_version():
    """Test Python versie"""
    print("🐍 Python Versie Check")
    print(f"Python versie: {sys.version}")
    print(f"Python executable: {sys.executable}")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is vereist")
        return False
    else:
        print("✅ Python versie OK")
        return True

def test_imports():
    """Test alle imports"""
    print("\n📦 Import Test")
    
    imports_to_test = [
        ('os', 'Standard library'),
        ('sys', 'Standard library'),
        ('asyncio', 'Standard library'),
        ('json', 'Standard library'),
        ('decimal', 'Standard library'),
        ('ccxt', 'Exchange library'),
        ('loguru', 'Logging library'),
        ('dotenv', 'Environment variables'),
        ('aiohttp', 'HTTP client'),
    ]
    
    failed_imports = []
    
    for module_name, description in imports_to_test:
        try:
            if module_name == 'dotenv':
                from dotenv import load_dotenv
            else:
                __import__(module_name)
            print(f"✅ {module_name} - {description}")
        except ImportError as e:
            print(f"❌ {module_name} - {description} - ERROR: {e}")
            failed_imports.append(module_name)
    
    return failed_imports

def test_config():
    """Test configuratie"""
    print("\n⚙️ Configuratie Test")
    
    try:
        # Test .env file
        if not os.path.exists('.env'):
            print("❌ .env file niet gevonden")
            return False
        
        print("✅ .env file gevonden")
        
        # Test config import
        from config import get_settings
        settings = get_settings()
        
        print("✅ Config module geïmporteerd")
        
        # Test settings
        if settings.telegram_bot_token:
            print("✅ Telegram bot token gevonden")
        else:
            print("❌ Telegram bot token ontbreekt")
        
        if settings.kucoin_api_key:
            print("✅ KuCoin API key gevonden")
        else:
            print("❌ KuCoin API key ontbreekt")
        
        if settings.mexc_api_key:
            print("✅ MEXC API key gevonden")
        else:
            print("❌ MEXC API key ontbreekt")
        
        return True
        
    except Exception as e:
        print(f"❌ Config test failed: {e}")
        traceback.print_exc()
        return False

def test_exchange_imports():
    """Test exchange imports"""
    print("\n🏦 Exchange Import Test")
    
    try:
        from exchanges.manager import ExchangeManager
        print("✅ Exchange Manager")
        
        from exchanges.kucoin import KuCoinExchange
        print("✅ KuCoin Exchange")
        
        from exchanges.mexc import MEXCExchange
        print("✅ MEXC Exchange")
        
        return True
        
    except Exception as e:
        print(f"❌ Exchange import failed: {e}")
        traceback.print_exc()
        return False

def test_strategy_imports():
    """Test strategy imports"""
    print("\n🤖 Strategy Import Test")
    
    try:
        from strategies.manager import StrategyManager
        print("✅ Strategy Manager")
        
        from strategies.daytrading import DayTradingStrategy
        print("✅ Day Trading Strategy")
        
        from strategies.scalping import ScalpingStrategy
        print("✅ Scalping Strategy")
        
        return True
        
    except Exception as e:
        print(f"❌ Strategy import failed: {e}")
        traceback.print_exc()
        return False

def test_analysis_imports():
    """Test analysis imports"""
    print("\n📊 Analysis Import Test")
    
    try:
        from analysis.market_analyzer import MarketAnalyzer
        print("✅ Market Analyzer")
        
        from analysis.technical_simple import TechnicalAnalyzer
        print("✅ Technical Analyzer")
        
        return True
        
    except Exception as e:
        print(f"❌ Analysis import failed: {e}")
        traceback.print_exc()
        return False

def test_telegram_imports():
    """Test telegram imports"""
    print("\n📱 Telegram Import Test")
    
    try:
        import aiohttp
        print("✅ aiohttp")
        
        import json
        print("✅ json")
        
        # Test if we can create a simple HTTP session
        import asyncio
        
        async def test_http():
            async with aiohttp.ClientSession() as session:
                return True
        
        # Run the test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(test_http())
        loop.close()
        
        print("✅ HTTP session test")
        
        return True
        
    except Exception as e:
        print(f"❌ Telegram import failed: {e}")
        traceback.print_exc()
        return False

def install_missing_packages(failed_imports):
    """Installeer ontbrekende packages"""
    if not failed_imports:
        return
    
    print(f"\n📦 Installeren van ontbrekende packages: {', '.join(failed_imports)}")
    
    import subprocess
    
    package_map = {
        'ccxt': 'ccxt',
        'loguru': 'loguru',
        'dotenv': 'python-dotenv',
        'aiohttp': 'aiohttp'
    }
    
    for package in failed_imports:
        if package in package_map:
            try:
                print(f"Installing {package_map[package]}...")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_map[package]])
                print(f"✅ {package_map[package]} installed")
            except subprocess.CalledProcessError as e:
                print(f"❌ Failed to install {package_map[package]}: {e}")

def main():
    """Main debug function"""
    print("🔍 Trading Bot Debug Tool")
    print("=" * 40)
    
    # Test Python version
    if not test_python_version():
        return
    
    # Test imports
    failed_imports = test_imports()
    
    if failed_imports:
        print(f"\n⚠️ Ontbrekende packages gevonden: {', '.join(failed_imports)}")
        install_missing_packages(failed_imports)
        
        # Test imports again
        print("\n🔄 Hertest imports...")
        failed_imports = test_imports()
        
        if failed_imports:
            print(f"\n❌ Nog steeds ontbrekende packages: {', '.join(failed_imports)}")
            print("Installeer handmatig met:")
            for package in failed_imports:
                print(f"pip install {package}")
            return
    
    # Test configuration
    if not test_config():
        print("\n❌ Configuratie problemen gevonden")
        return
    
    # Test exchange imports
    if not test_exchange_imports():
        print("\n❌ Exchange import problemen gevonden")
        return
    
    # Test strategy imports
    if not test_strategy_imports():
        print("\n❌ Strategy import problemen gevonden")
        return
    
    # Test analysis imports
    if not test_analysis_imports():
        print("\n❌ Analysis import problemen gevonden")
        return
    
    # Test telegram imports
    if not test_telegram_imports():
        print("\n❌ Telegram import problemen gevonden")
        return
    
    print("\n" + "=" * 40)
    print("🎉 Alle tests geslaagd!")
    print("\nJe kunt nu de bot starten met:")
    print("python simple_bot.py          # Console bot")
    print("python telegram_simple.py     # Telegram bot")

if __name__ == "__main__":
    main()
