"""
Day Trading Strategy with AI-powered analysis
"""
import asyncio
from decimal import Decimal
from typing import Dict, List
# import pandas as pd  # Disabled for Python 3.13 compatibility
# import numpy as np   # Disabled for Python 3.13 compatibility
from .base import BaseStrategy, TradingSignal, SignalType, Position, StrategyConfig
from analysis.technical_simple import TechnicalAnalyzer
from analysis.ai_analyzer import AIAnalyzer
from loguru import logger

class DayTradingStrategy(BaseStrategy):
    """
    AI-powered day trading strategy with automatic stop-loss and trailing stops

    Features:
    - Intraday momentum trading
    - AI-powered entry/exit signals
    - Automatic stop-loss management
    - Trailing stops
    - Volume analysis
    - Support/resistance levels
    """

    def __init__(self, config: StrategyConfig, use_ai_stop=True):
        super().__init__(config)
        self.use_ai_stop = use_ai_stop
        self.technical_analyzer = TechnicalAnalyzer()
        self.ai_analyzer = AIAnalyzer()
        self.min_volume_ratio = 1.5  # Minimum volume vs average
        self.momentum_threshold = 0.02  # 2% momentum threshold

    def _ai_stop_loss(self, market_data, signal):
        price = market_data['price']
        high = market_data.get('high_24h', price)
        low = market_data.get('low_24h', price)
        volatility = (high - low) / price
        base_stop = 0.015
        stop_pct = max(base_stop, min(0.05, volatility * 0.7))
        ai_conf = getattr(signal, 'confidence', None)
        if ai_conf is not None:
            stop_pct = stop_pct * (1.2 - min(ai_conf, 1.0))
        return price * (1 - stop_pct) if signal.signal in ['strong_buy', 'buy'] else price * (1 + stop_pct)

    def _ai_trailing_stop(self, entry_price, current_price, best_price, base_trail=0.01):
        trail_pct = base_trail
        trailing_stop = best_price * (1 - trail_pct)
        return max(trailing_stop, entry_price * (1 - trail_pct))

    async def analyze(self, symbol: str, market_data: Dict) -> TradingSignal:
        """Analyze market for day trading opportunities"""
        try:
            # Get technical indicators
            technical_data = await self.technical_analyzer.analyze(symbol, market_data, "15m")

            # Get AI analysis
            ai_analysis = await self.ai_analyzer.analyze_market(symbol, market_data)

            # Current price
            current_price = Decimal(str(market_data['price']))

            # Volume analysis
            volume_signal = await self._analyze_volume(market_data)

            # Momentum analysis
            momentum_signal = await self._analyze_momentum(technical_data)

            # Support/Resistance levels
            levels = await self._find_support_resistance(market_data)

            # Combine signals
            signal_strength = self._combine_signals(
                technical_data, ai_analysis, volume_signal, momentum_signal, levels
            )

            # Determine signal type
            if signal_strength > self.config.min_confidence:
                signal_type = SignalType.BUY
                confidence = float(signal_strength)

                # Calculate stop loss and take profit
                stop_loss = current_price * (1 - self.config.stop_loss_percentage / 100)
                take_profit = current_price * (1 + self.config.take_profit_percentage / 100)

                reasoning = f"Day trading signal: Technical={technical_data.get('score', 0):.2f}, AI={ai_analysis.get('confidence', 0):.2f}, Volume={volume_signal:.2f}, Momentum={momentum_signal:.2f}"

            elif signal_strength < -self.config.min_confidence:
                signal_type = SignalType.SELL
                confidence = float(abs(signal_strength))

                stop_loss = current_price * (1 + self.config.stop_loss_percentage / 100)
                take_profit = current_price * (1 - self.config.take_profit_percentage / 100)

                reasoning = f"Day trading sell signal: Technical={technical_data.get('score', 0):.2f}, AI={ai_analysis.get('confidence', 0):.2f}"

            else:
                signal_type = SignalType.HOLD
                confidence = 0.5
                stop_loss = None
                take_profit = None
                reasoning = "No clear day trading signal"

            signal = TradingSignal(
                signal=signal_type,
                confidence=confidence,
                price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                reasoning=reasoning
            )

            if self.use_ai_stop:
                signal.stop_loss = self._ai_stop_loss(market_data, signal)
                # trailing stop kan je bijhouden in je order management

            return signal

        except Exception as e:
            logger.error(f"Error in day trading analysis for {symbol}: {e}")
            return TradingSignal(
                signal=SignalType.HOLD,
                confidence=0.0,
                price=Decimal(str(market_data['price'])),
                reasoning=f"Analysis error: {str(e)}"
            )

    async def _analyze_volume(self, market_data: Dict) -> float:
        """Analyze volume for day trading signals"""
        try:
            current_volume = market_data.get('volume', 0)
            avg_volume = market_data.get('avg_volume_24h', current_volume)

            if avg_volume == 0:
                return 0.0

            volume_ratio = current_volume / avg_volume

            # High volume = stronger signal
            if volume_ratio > self.min_volume_ratio:
                return min(volume_ratio / 3, 1.0)  # Cap at 1.0
            else:
                return -0.2  # Penalize low volume

        except Exception:
            return 0.0

    async def _analyze_momentum(self, technical_data: Dict) -> float:
        """Analyze momentum indicators"""
        try:
            rsi = technical_data.get('rsi', 50)
            macd_signal = technical_data.get('macd_signal', 0)
            price_change = technical_data.get('price_change_1h', 0)

            momentum_score = 0.0

            # RSI momentum
            if 30 < rsi < 70:  # Not overbought/oversold
                if rsi > 55:
                    momentum_score += 0.3
                elif rsi < 45:
                    momentum_score -= 0.3

            # MACD momentum
            if macd_signal > 0:
                momentum_score += 0.4
            elif macd_signal < 0:
                momentum_score -= 0.4

            # Price momentum
            if abs(price_change) > self.momentum_threshold:
                if price_change > 0:
                    momentum_score += 0.3
                else:
                    momentum_score -= 0.3

            return max(-1.0, min(1.0, momentum_score))

        except Exception:
            return 0.0

    async def _find_support_resistance(self, market_data: Dict) -> Dict:
        """Find support and resistance levels"""
        try:
            # Simplified support/resistance based on recent highs/lows
            high_24h = Decimal(str(market_data.get('high_24h', 0)))
            low_24h = Decimal(str(market_data.get('low_24h', 0)))
            current_price = Decimal(str(market_data['price']))

            # Calculate levels
            resistance = high_24h
            support = low_24h

            # Position relative to range
            if high_24h > low_24h:
                position_in_range = (current_price - low_24h) / (high_24h - low_24h)
            else:
                position_in_range = 0.5

            return {
                'resistance': resistance,
                'support': support,
                'position_in_range': float(position_in_range)
            }

        except Exception:
            return {'resistance': 0, 'support': 0, 'position_in_range': 0.5}

    def _combine_signals(self, technical_data: Dict, ai_analysis: Dict,
                        volume_signal: float, momentum_signal: float, levels: Dict) -> float:
        """Combine all signals into final score"""

        # Weights for different signals
        weights = {
            'technical': 0.3,
            'ai': 0.3,
            'volume': 0.2,
            'momentum': 0.2
        }

        # Get individual scores
        technical_score = technical_data.get('score', 0) / 100  # Normalize to -1 to 1
        ai_score = (ai_analysis.get('confidence', 0.5) - 0.5) * 2  # Convert 0-1 to -1 to 1

        # Combine weighted scores
        final_score = (
            weights['technical'] * technical_score +
            weights['ai'] * ai_score +
            weights['volume'] * volume_signal +
            weights['momentum'] * momentum_signal
        )

        # Adjust based on support/resistance
        position_in_range = levels.get('position_in_range', 0.5)
        if position_in_range < 0.2:  # Near support
            final_score += 0.1
        elif position_in_range > 0.8:  # Near resistance
            final_score -= 0.1

        return max(-1.0, min(1.0, final_score))

    async def should_enter_position(self, signal: TradingSignal, portfolio_value: Decimal) -> bool:
        """Determine if we should enter a day trading position"""

        # Check if we have too many positions
        if len(self.positions) >= self.config.max_positions:
            return False

        # Check signal confidence
        if signal.confidence < float(self.config.min_confidence):
            return False

        # Check if signal is actionable
        if signal.signal in [SignalType.BUY, SignalType.SELL]:
            return True

        return False

    async def should_exit_position(self, position: Position, current_price: Decimal) -> bool:
        """Determine if we should exit a day trading position"""

        # Update trailing stop
        new_stop = await self.update_trailing_stop(position, current_price)
        if new_stop:
            position.stop_loss = new_stop

        # Check stop loss
        if position.stop_loss:
            if position.side == 'long' and current_price <= position.stop_loss:
                return True
            elif position.side == 'short' and current_price >= position.stop_loss:
                return True

        # Check take profit
        if position.take_profit:
            if position.side == 'long' and current_price >= position.take_profit:
                return True
            elif position.side == 'short' and current_price <= position.take_profit:
                return True

        # Day trading: close positions at end of day (simplified)
        # In real implementation, you'd check actual time

        return False
