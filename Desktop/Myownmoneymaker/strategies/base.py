"""
Base trading strategy interface
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from decimal import Decimal
from enum import Enum

class SignalType(Enum):
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"

@dataclass
class TradingSignal:
    """Trading signal with confidence and reasoning"""
    signal: SignalType
    confidence: float  # 0.0 to 1.0
    price: Decimal
    amount: Optional[Decimal] = None
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None
    reasoning: str = ""
    timestamp: int = 0

@dataclass
class Position:
    """Trading position"""
    symbol: str
    side: str  # 'long' or 'short'
    entry_price: Decimal
    amount: Decimal
    current_price: Decimal
    stop_loss: Optional[Decimal] = None
    take_profit: Optional[Decimal] = None
    trailing_stop: Optional[Decimal] = None
    pnl: Decimal = Decimal('0')
    pnl_percentage: Decimal = Decimal('0')
    timestamp: int = 0

@dataclass
class StrategyConfig:
    """Strategy configuration"""
    name: str
    enabled: bool = True
    risk_percentage: Decimal = Decimal('2')  # % of portfolio per trade
    max_positions: int = 3
    stop_loss_percentage: Decimal = Decimal('2')  # % stop loss
    take_profit_percentage: Decimal = Decimal('6')  # % take profit
    trailing_stop_percentage: Decimal = Decimal('1')  # % trailing stop
    min_confidence: Decimal = Decimal('0.7')  # Minimum signal confidence
    timeframe: str = "1h"
    symbols: List[str] = None
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = ["BTC/USDT", "ETH/USDT"]

class BaseStrategy(ABC):
    """Base class for all trading strategies"""
    
    def __init__(self, config: StrategyConfig):
        self.config = config
        self.positions: Dict[str, Position] = {}
        self.active = True
    
    @abstractmethod
    async def analyze(self, symbol: str, market_data: Dict) -> TradingSignal:
        """Analyze market data and generate trading signal"""
        pass
    
    @abstractmethod
    async def should_enter_position(self, signal: TradingSignal, portfolio_value: Decimal) -> bool:
        """Determine if we should enter a new position"""
        pass
    
    @abstractmethod
    async def should_exit_position(self, position: Position, current_price: Decimal) -> bool:
        """Determine if we should exit an existing position"""
        pass
    
    async def calculate_position_size(self, signal: TradingSignal, portfolio_value: Decimal) -> Decimal:
        """Calculate position size based on risk management"""
        risk_amount = portfolio_value * (self.config.risk_percentage / 100)
        
        if signal.stop_loss:
            price_diff = abs(signal.price - signal.stop_loss)
            if price_diff > 0:
                position_size = risk_amount / price_diff
                return min(position_size, portfolio_value * Decimal('0.1'))  # Max 10% of portfolio
        
        # Fallback: use fixed percentage
        return portfolio_value * (self.config.risk_percentage / 100) / signal.price
    
    async def update_trailing_stop(self, position: Position, current_price: Decimal) -> Optional[Decimal]:
        """Update trailing stop loss"""
        if not position.trailing_stop:
            return None
        
        trailing_percentage = self.config.trailing_stop_percentage / 100
        
        if position.side == 'long':
            # For long positions, trailing stop moves up with price
            new_stop = current_price * (1 - trailing_percentage)
            if position.stop_loss is None or new_stop > position.stop_loss:
                return new_stop
        else:
            # For short positions, trailing stop moves down with price
            new_stop = current_price * (1 + trailing_percentage)
            if position.stop_loss is None or new_stop < position.stop_loss:
                return new_stop
        
        return position.stop_loss
    
    async def calculate_pnl(self, position: Position, current_price: Decimal) -> tuple:
        """Calculate PnL for a position"""
        if position.side == 'long':
            pnl = (current_price - position.entry_price) * position.amount
            pnl_percentage = ((current_price - position.entry_price) / position.entry_price) * 100
        else:
            pnl = (position.entry_price - current_price) * position.amount
            pnl_percentage = ((position.entry_price - current_price) / position.entry_price) * 100
        
        return pnl, pnl_percentage
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information"""
        return {
            "name": self.config.name,
            "enabled": self.config.enabled,
            "active": self.active,
            "positions": len(self.positions),
            "symbols": self.config.symbols,
            "risk_percentage": float(self.config.risk_percentage),
            "timeframe": self.config.timeframe
        }
