"""
Advanced Risk Management Module
"""
from typing import Dict, List, Optional
from decimal import Decimal
from loguru import logger
import statistics

class RiskManager:
    def __init__(self, max_risk_per_trade: float = 0.02, max_correlation_risk: float = 0.5):
        self.max_risk_per_trade = max_risk_per_trade  # Maximum 2% risk per trade
        self.max_correlation_risk = max_correlation_risk
        self.open_positions = {}
        self.trade_history = []
        self.daily_pnl = []
        
    def calculate_position_size(self, capital: float, entry_price: float, 
                              stop_loss: float, confidence: float) -> Dict:
        """Calculate optimal position size based on risk parameters"""
        try:
            # Base risk calculation
            risk_amount = capital * self.max_risk_per_trade
            
            # Adjust risk based on confidence
            adjusted_risk = risk_amount * min(confidence, 1.0)
            
            # Calculate position size
            price_risk = abs(entry_price - stop_loss)
            if price_risk == 0:
                price_risk = entry_price * 0.02  # Default 2% stop loss
                
            position_size = adjusted_risk / price_risk
            
            # Maximum position size checks
            max_position = capital * 0.2  # Never risk more than 20% of capital
            position_size = min(position_size, max_position / entry_price)
            
            return {
                'size': position_size,
                'risk_amount': adjusted_risk,
                'max_loss': adjusted_risk
            }
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return {'size': 0, 'risk_amount': 0, 'max_loss': 0}
            
    def validate_trade(self, symbol: str, trade_type: str, 
                      risk_metrics: Dict, market_data: Dict) -> bool:
        """Validate if trade meets risk management criteria"""
        try:
            # Check overall portfolio risk
            total_risk = sum(pos['risk_amount'] for pos in self.open_positions.values())
            if total_risk + risk_metrics['risk_amount'] > capital * 0.1:  # Max 10% total risk
                return False
                
            # Check correlation risk
            if len(self.open_positions) > 0:
                correlation = self._calculate_correlation(symbol, list(self.open_positions.keys()))
                if correlation > self.max_correlation_risk:
                    return False
                    
            # Check market volatility
            if self._is_market_too_volatile(market_data):
                return False
                
            # Check if we're overtrading
            if self._is_overtrading():
                return False
                
            return True
            
        except Exception as e:
            logger.error(f"Error validating trade: {e}")
            return False
            
    def update_position(self, symbol: str, pnl: float, closed: bool = False):
        """Update position tracking and risk metrics"""
        try:
            if closed:
                if symbol in self.open_positions:
                    position = self.open_positions.pop(symbol)
                    self.trade_history.append({
                        'symbol': symbol,
                        'pnl': pnl,
                        'risk_amount': position['risk_amount']
                    })
                    self.daily_pnl.append(pnl)
            else:
                if symbol in self.open_positions:
                    self.open_positions[symbol]['current_pnl'] = pnl
                    
        except Exception as e:
            logger.error(f"Error updating position: {e}")
            
    def _calculate_correlation(self, symbol: str, other_symbols: List[str]) -> float:
        """Calculate price correlation between assets"""
        try:
            # In real implementation, calculate actual price correlation
            return 0.3  # Placeholder
        except Exception:
            return 1.0  # Conservative fallback
            
    def _is_market_too_volatile(self, market_data: Dict) -> bool:
        """Check if market volatility is too high"""
        try:
            price = market_data['price']
            high_24h = market_data.get('high_24h', price)
            low_24h = market_data.get('low_24h', price)
            
            volatility = (high_24h - low_24h) / low_24h * 100
            return volatility > 15  # More than 15% daily range
            
        except Exception:
            return True  # Conservative fallback
            
    def _is_overtrading(self) -> bool:
        """Check for overtrading based on recent activity"""
        try:
            if len(self.daily_pnl) < 10:
                return False
                
            # Check if we're having too many losses
            recent_trades = self.daily_pnl[-10:]
            losing_trades = sum(1 for pnl in recent_trades if pnl < 0)
            
            return losing_trades >= 7  # Too many recent losses
            
        except Exception:
            return True  # Conservative fallback