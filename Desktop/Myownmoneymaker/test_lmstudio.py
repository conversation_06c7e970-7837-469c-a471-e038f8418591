import openai

openai.api_base = "http://127.0.0.1:1234/v1"
openai.api_key = "lm-studio"  # mag elke string zijn

response = openai.ChatCompletion.create(
    model="deepseek-coder-33b-instruct",
    messages=[
        {"role": "system", "content": "Je bent een behulpzame programmeerassistent."},
        {"role": "user", "content": "Schrijf een Python functie die twee getallen optelt."}
    ],
    temperature=0.2,
    max_tokens=256
)

print(response.choices[0].message["content"])
