#!/bin/bash
# Trading Bot Startup Script voor Mac/Linux

echo "🤖 Trading Bot Startup"
echo "======================"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 not found. Please install Python 3.8 or higher."
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "config.py" ]; then
    echo "❌ Not in the correct directory. Please run this from the Myownmoneymaker folder."
    exit 1
fi

# Install dependencies if needed
echo "📦 Installing dependencies..."
python3 -m pip install --upgrade pip
python3 -m pip install -r requirements.txt

# Check .env file
if [ ! -f ".env" ]; then
    echo "❌ .env file not found!"
    echo "Please create a .env file with your API keys."
    exit 1
fi

echo "✅ Setup complete!"
echo ""

# Ask user which bot to start
echo "🎯 Choose bot type:"
echo "1. Console <PERSON> (Terminal interface)"
echo "2. Telegram <PERSON> (Mobile interface)"
echo "3. Simple Test (Check connections)"
echo ""

read -p "Enter choice (1, 2, or 3): " choice

case $choice in
    1)
        echo "🚀 Starting Console Bot..."
        python3 simple_bot.py
        ;;
    2)
        echo "🚀 Starting Telegram Bot..."
        python3 telegram_simple.py
        ;;
    3)
        echo "🧪 Running connection test..."
        python3 test_exchanges.py
        ;;
    *)
        echo "❌ Invalid choice. Starting console bot..."
        python3 simple_bot.py
        ;;
esac
