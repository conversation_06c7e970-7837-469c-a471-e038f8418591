"""
Main Bot Application Entry Point
"""
import asyncio
from loguru import logger
from config.settings import Settings
from core.exchange_manager import ExchangeManager
from core.market_analyzer import MarketAnalyzer
from core.technical import TechnicalAnalyzer
from core.risk_manager import RiskManager
from core.trade_manager import TradeManager
from core.notifier import Notifier

class TradingBot:
    def __init__(self):
        self.settings = Settings()
        self.exchange_manager = ExchangeManager()
        self.market_analyzer = MarketAnalyzer()
        self.technical_analyzer = TechnicalAnalyzer()
        self.risk_manager = RiskManager()
        self.trade_manager = TradeManager()
        self.notifier = Notifier()

    async def start(self):
        """Start the trading bot"""
        try:
            logger.info("Starting trading bot...")
            
            # Initialize components
            await self.exchange_manager.initialize()
            await self.market_analyzer.initialize()
            await self.risk_manager.initialize()
            await self.trade_manager.initialize()
            await self.notifier.initialize()

            # Start main trading loop
            await self.run_trading_loop()

        except Exception as e:
            logger.error(f"Error starting bot: {e}")
            await self.shutdown()

    async def run_trading_loop(self):
        """Main trading loop"""
        while True:
            try:
                # Get market data
                market_data = await self.exchange_manager.get_market_data()
                
                # Analyze market conditions
                market_analysis = await self.market_analyzer.analyze(market_data)
                technical_analysis = await self.technical_analyzer.analyze(market_data)
                
                # Check risk parameters
                if await self.risk_manager.check_risk_levels(market_analysis):
                    # Execute trades if risk levels are acceptable
                    trade_signals = await self.trade_manager.generate_signals(
                        market_analysis,
                        technical_analysis
                    )
                    
                    if trade_signals:
                        await self.execute_trades(trade_signals)
                
                # Wait for next interval
                await asyncio.sleep(self.settings.TRADING_INTERVAL)
                
            except Exception as e:
                logger.error(f"Error in trading loop: {e}")
                await asyncio.sleep(self.settings.ERROR_RETRY_DELAY)

    async def execute_trades(self, signals):
        """Execute trade signals"""
        for signal in signals:
            try:
                # Execute trade
                trade_result = await self.trade_manager.execute_trade(signal)
                
                # Notify about trade
                await self.notifier.send_trade_notification(trade_result)
                
            except Exception as e:
                logger.error(f"Error executing trade: {e}")

    async def shutdown(self):
        """Graceful shutdown of the bot"""
        try:
            logger.info("Shutting down trading bot...")
            await self.exchange_manager.shutdown()
            await self.trade_manager.shutdown()
            await self.notifier.shutdown()
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

if __name__ == "__main__":
    bot = TradingBot()
    asyncio.run(bot.start())
