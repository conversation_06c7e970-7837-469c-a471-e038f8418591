"""
Test trading strategies
"""
import asyncio
from decimal import Decimal
from exchanges.manager import ExchangeManager
from strategies.manager import StrategyManager
from analysis.market_analyzer import MarketAnalyzer

async def test_market_analysis():
    """Test market analysis"""
    print("📊 Testing market analysis...")
    
    try:
        exchange_manager = ExchangeManager()
        await exchange_manager.connect_all()
        
        market_analyzer = MarketAnalyzer(exchange_manager)
        
        # Get analysis for BTC
        analysis = market_analyzer.get_latest_analysis("BTC/USDT")
        if not analysis:
            print("  ⏳ No analysis yet, running one cycle...")
            await market_analyzer._perform_market_analysis()
            analysis = market_analyzer.get_latest_analysis("BTC/USDT")
        
        if analysis:
            print("  ✅ Market analysis working")
            market_data = analysis.get('market_data', {})
            print(f"  📈 BTC Price: ${market_data.get('price', 0):.2f}")
            
            alerts = analysis.get('alerts', [])
            print(f"  🚨 Alerts: {len(alerts)}")
        else:
            print("  ❌ No market analysis data")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Market analysis failed: {e}")
        return False

async def test_strategies():
    """Test trading strategies"""
    print("\n🤖 Testing trading strategies...")
    
    try:
        exchange_manager = ExchangeManager()
        await exchange_manager.connect_all()
        
        strategy_manager = StrategyManager(exchange_manager)
        
        # Get strategy status
        status = strategy_manager.get_strategy_status()
        print(f"  ✅ {len(status)} strategies loaded:")
        
        for name, info in status.items():
            enabled = "✅" if info['enabled'] else "❌"
            print(f"    {enabled} {name}: {info['timeframe']}, Risk: {info['risk_percentage']}%")
        
        # Test a single strategy analysis
        print("\n  🧪 Testing day trading strategy analysis...")
        daytrading_strategy = strategy_manager.strategies.get('daytrading')
        
        if daytrading_strategy:
            # Get market data
            tickers = await exchange_manager.get_ticker_from_all("BTC/USDT")
            if tickers:
                best_ticker = max(tickers.values(), key=lambda t: t.volume)
                market_data = {
                    'price': float(best_ticker.last),
                    'bid': float(best_ticker.bid),
                    'ask': float(best_ticker.ask),
                    'high_24h': float(best_ticker.high),
                    'low_24h': float(best_ticker.low),
                    'volume': float(best_ticker.volume),
                    'timestamp': best_ticker.timestamp
                }
                
                # Analyze with strategy
                signal = await daytrading_strategy.analyze("BTC/USDT", market_data)
                
                print(f"    📊 Signal: {signal.signal.value}")
                print(f"    🎯 Confidence: {signal.confidence:.2f}")
                print(f"    💭 Reasoning: {signal.reasoning}")
                
                if signal.stop_loss:
                    print(f"    🛡️ Stop Loss: ${signal.stop_loss:.2f}")
                if signal.take_profit:
                    print(f"    🎯 Take Profit: ${signal.take_profit:.2f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Strategy test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Trading Strategies Test")
    print("=" * 50)
    
    # Test market analysis
    analysis_ok = await test_market_analysis()
    
    # Test strategies
    strategies_ok = await test_strategies()
    
    print("\n" + "=" * 50)
    if analysis_ok and strategies_ok:
        print("🎉 All strategy tests passed!")
        print("\n📋 Summary:")
        print("✅ Exchange connections working")
        print("✅ Market analysis working")
        print("✅ Trading strategies working")
        print("\n🚀 Ready to start full bot!")
        print("\nNext steps:")
        print("1. Set up your Telegram bot token")
        print("2. Run: python main.py")
    else:
        print("❌ Some strategy tests failed.")

if __name__ == "__main__":
    asyncio.run(main())
