"""
Test exchange connections
"""
import asyncio
from exchanges.manager import <PERSON><PERSON>anager
from config import get_settings

async def test_exchange_connections():
    """Test connections to exchanges"""
    print("🔄 Testing exchange connections...")
    
    try:
        # Initialize exchange manager
        exchange_manager = ExchangeManager()
        
        # Test connections
        results = await exchange_manager.connect_all()
        
        print("\n📊 Connection Results:")
        for exchange, connected in results.items():
            status = "✅ Connected" if connected else "❌ Failed"
            print(f"  {exchange.upper()}: {status}")
        
        # If any exchange connected, test basic functionality
        connected_exchanges = [name for name, connected in results.items() if connected]
        
        if connected_exchanges:
            print(f"\n🧪 Testing basic functionality with {connected_exchanges[0]}...")
            
            # Test ticker
            try:
                tickers = await exchange_manager.get_ticker_from_all("BTC/USDT")
                if tickers:
                    for exchange_name, ticker in tickers.items():
                        print(f"  {exchange_name}: BTC/USDT = ${ticker.last:.2f}")
                else:
                    print("  ❌ No ticker data received")
            except Exception as e:
                print(f"  ❌ Ticker test failed: {e}")
        
        return any(results.values())
        
    except Exception as e:
        print(f"❌ Exchange test failed: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Exchange Connection Test")
    print("=" * 40)
    
    # Check configuration first
    settings = get_settings()
    if not settings.validate():
        print("❌ Configuration invalid. Please check your .env file.")
        return
    
    # Test exchanges
    success = await test_exchange_connections()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 Exchange tests passed!")
        print("\nYou can now run the full bot with:")
        print("python main.py")
    else:
        print("❌ Exchange tests failed.")
        print("\nPlease check:")
        print("1. Your API keys in .env file")
        print("2. API permissions (trading enabled)")
        print("3. Internet connection")

if __name__ == "__main__":
    asyncio.run(main())
