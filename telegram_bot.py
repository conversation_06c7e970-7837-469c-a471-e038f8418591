"""
Working Telegram Trading Bot (Python 3.13 compatible)
"""
import asyncio
import sys
import os
from decimal import Decimal
from loguru import logger
from config import get_settings
from exchanges.manager import ExchangeManager
from strategies.manager import StrategyManager
from analysis.market_analyzer import MarketAnalyzer

# Set timezone before importing telegram
os.environ['TZ'] = 'UTC'

# Try different telegram bot approaches
try:
    from telegram import Update
    from telegram.ext import Application, CommandHandler, CallbackQueryHandler, ContextTypes
    TELEGRAM_AVAILABLE = True
except ImportError as e:
    logger.error(f"Telegram import failed: {e}")
    TELEGRAM_AVAILABLE = False

class TelegramTradingBot:
    """Telegram Trading Bot with fallback compatibility"""
    
    def __init__(self):
        self.settings = get_settings()
        self.exchange_manager = None
        self.strategy_manager = None
        self.market_analyzer = None
        self.application = None
    
    def _is_authorized(self, user_id: int) -> bool:
        """Check if user is authorized (supports multiple admin IDs)"""
        return user_id in self.settings.telegram_admin_user_ids
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start command handler"""
        user_id = update.effective_user.id
        
        if not self._is_authorized(user_id):
            await update.message.reply_text("❌ Je bent niet geautoriseerd om deze bot te gebruiken.")
            logger.warning(f"Unauthorized access attempt by user ID: {user_id}")
            return
        
        welcome_message = """
🤖 **Welkom bij de Trading Bot!**

Deze bot helpt je met trading op KuCoin en MEXC exchanges.

**Beschikbare commando's:**
/balance - Toon portfolio balances
/price <symbol> - Toon prijs van een coin (bijv. /price BTC/USDT)
/analysis - Toon marktanalyse
/strategies - Toon trading strategieën
/positions - Toon actieve posities
/start_trading - Start automatische trading
/stop_trading - Stop automatische trading
/help - Toon deze help

**Voorbeelden:**
• `/price BTC/USDT` - Bitcoin prijs
• `/analysis` - Marktanalyse rapport

⚠️ **Let op:** Alle trading acties zijn echt! Wees voorzichtig.
        """
        
        await update.message.reply_text(welcome_message, parse_mode='Markdown')
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Help command handler"""
        if not self._is_authorized(update.effective_user.id):
            return
        await self.start_command(update, context)
    
    async def balance_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Balance command handler"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return
        
        await update.message.reply_text("💰 Balances ophalen...")
        
        try:
            all_balances = await self.exchange_manager.get_all_balances()
            
            message = "💰 **Portfolio Balances**\n\n"
            
            for exchange_name, balances in all_balances.items():
                message += f"**{exchange_name.upper()}:**\n"
                
                if not balances:
                    message += "  Geen balances gevonden\n\n"
                    continue
                
                non_zero_balances = {k: v for k, v in balances.items() if v.total > 0}
                
                if not non_zero_balances:
                    message += "  Geen balances > 0\n\n"
                    continue
                
                for currency, balance in sorted(non_zero_balances.items()):
                    message += f"  {currency}: {balance.total:.8f}\n"
                    if balance.used > 0:
                        message += f"    (Used: {balance.used:.8f})\n"
                
                message += "\n"
            
            await update.message.reply_text(message, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error fetching balances: {e}")
            await update.message.reply_text(f"❌ Fout bij ophalen balances: {str(e)}")
    
    async def price_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Price command handler"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return
        
        if not context.args:
            await update.message.reply_text("❌ Gebruik: /price <symbol>\nVoorbeeld: /price BTC/USDT")
            return
        
        symbol = context.args[0].upper()
        
        await update.message.reply_text(f"📊 Prijzen ophalen voor {symbol}...")
        
        try:
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)
            
            if not tickers:
                await update.message.reply_text(f"❌ Geen prijsdata gevonden voor {symbol}")
                return
            
            message = f"📊 **Prijzen voor {symbol}**\n\n"
            
            for exchange_name, ticker in tickers.items():
                message += f"**{exchange_name.upper()}:**\n"
                message += f"  Last: ${ticker.last:.8f}\n"
                message += f"  Bid: ${ticker.bid:.8f}\n"
                message += f"  Ask: ${ticker.ask:.8f}\n"
                message += f"  24h High: ${ticker.high:.8f}\n"
                message += f"  24h Low: ${ticker.low:.8f}\n"
                message += f"  Volume: {ticker.volume:.2f}\n\n"
            
            await update.message.reply_text(message, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error fetching price for {symbol}: {e}")
            await update.message.reply_text(f"❌ Fout bij ophalen prijs: {str(e)}")
    
    async def analysis_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Market analysis command"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return
        
        try:
            await update.message.reply_text("📊 Marktanalyse ophalen...")
            
            if not self.market_analyzer:
                await update.message.reply_text("❌ Marktanalyse niet beschikbaar.")
                return
            
            # Get or generate analysis
            latest = self.market_analyzer.get_latest_analysis()
            if not latest:
                await self.market_analyzer._perform_market_analysis()
                latest = self.market_analyzer.get_latest_analysis()
            
            if latest:
                message = "📈 **Marktanalyse**\n\n"
                
                for symbol, analysis in latest.items():
                    market_data = analysis.get('market_data', {})
                    alerts = analysis.get('alerts', [])
                    
                    price_change = market_data.get('price_change_24h', 0)
                    change_emoji = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
                    
                    message += f"{change_emoji} **{symbol}**\n"
                    message += f"  Prijs: ${market_data.get('price', 0):.2f}\n"
                    message += f"  24h: {price_change:+.2f}%\n"
                    message += f"  Alerts: {len(alerts)}\n\n"
                
                await update.message.reply_text(message, parse_mode='Markdown')
            else:
                await update.message.reply_text("❌ Geen analysedata beschikbaar.")
                
        except Exception as e:
            logger.error(f"Error getting analysis: {e}")
            await update.message.reply_text(f"❌ Fout bij ophalen analyse: {str(e)}")
    
    async def strategies_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Show strategies status"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return
        
        try:
            if not self.strategy_manager:
                await update.message.reply_text("❌ Strategy manager niet beschikbaar.")
                return
            
            status = self.strategy_manager.get_strategy_status()
            
            message = "🤖 **Trading Strategieën**\n\n"
            
            for name, info in status.items():
                status_emoji = "✅" if info['enabled'] else "❌"
                active_emoji = "🟢" if info['active'] else "🔴"
                
                message += f"{status_emoji} **{name}**\n"
                message += f"   Status: {active_emoji} {'Actief' if info['active'] else 'Inactief'}\n"
                message += f"   Posities: {info['positions']}\n"
                message += f"   Timeframe: {info['timeframe']}\n"
                message += f"   Risico: {info['risk_percentage']}%\n\n"
            
            await update.message.reply_text(message, parse_mode='Markdown')
            
        except Exception as e:
            logger.error(f"Error getting strategies: {e}")
            await update.message.reply_text(f"❌ Fout bij ophalen strategieën: {str(e)}")
    
    async def start_trading_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Start automated trading"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return
        
        try:
            if not self.strategy_manager:
                await update.message.reply_text("❌ Strategy manager niet beschikbaar.")
                return
            
            await update.message.reply_text("🤖 Automatische trading starten...")
            
            # Start market analyzer
            if self.market_analyzer:
                asyncio.create_task(self.market_analyzer.start_analysis())
            
            # Start strategy manager
            asyncio.create_task(self.strategy_manager.start_automated_trading())
            
            await update.message.reply_text("✅ Automatische trading gestart!")
            
        except Exception as e:
            logger.error(f"Error starting trading: {e}")
            await update.message.reply_text(f"❌ Fout bij starten trading: {str(e)}")
    
    async def stop_trading_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Stop automated trading"""
        if not self._is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ Niet geautoriseerd.")
            return
        
        try:
            if not self.strategy_manager:
                await update.message.reply_text("❌ Strategy manager niet beschikbaar.")
                return
            
            await update.message.reply_text("🛑 Automatische trading stoppen...")
            
            # Stop components
            if self.market_analyzer:
                self.market_analyzer.stop_analysis()
            
            if self.strategy_manager:
                self.strategy_manager.stop_automated_trading()
            
            await update.message.reply_text("✅ Automatische trading gestopt!")
            
        except Exception as e:
            logger.error(f"Error stopping trading: {e}")
            await update.message.reply_text(f"❌ Fout bij stoppen trading: {str(e)}")
    
    async def initialize(self):
        """Initialize all components"""
        logger.info("🚀 Initializing Telegram Trading Bot...")
        
        # Validate settings
        if not self.settings.validate():
            logger.error("❌ Invalid configuration. Please check your .env file.")
            return False
        
        # Initialize exchange manager
        self.exchange_manager = ExchangeManager()
        
        # Connect to exchanges
        logger.info("Connecting to exchanges...")
        connection_results = await self.exchange_manager.connect_all()
        
        for exchange, connected in connection_results.items():
            if connected:
                logger.info(f"✅ Connected to {exchange}")
            else:
                logger.error(f"❌ Failed to connect to {exchange}")
        
        if not any(connection_results.values()):
            logger.error("No exchanges connected. Exiting...")
            return False
        
        # Initialize strategy manager
        self.strategy_manager = StrategyManager(self.exchange_manager)
        
        # Initialize market analyzer
        self.market_analyzer = MarketAnalyzer(self.exchange_manager)
        
        logger.info("✅ All components initialized")
        return True
    
    async def run(self):
        """Run the Telegram bot"""
        if not TELEGRAM_AVAILABLE:
            logger.error("❌ Telegram library not available. Use simple_bot.py instead.")
            return
        
        try:
            # Initialize components
            if not await self.initialize():
                return
            
            # Create application without job queue to avoid timezone issues
            self.application = Application.builder().token(self.settings.telegram_bot_token).build()
            
            # Add command handlers
            self.application.add_handler(CommandHandler("start", self.start_command))
            self.application.add_handler(CommandHandler("help", self.help_command))
            self.application.add_handler(CommandHandler("balance", self.balance_command))
            self.application.add_handler(CommandHandler("price", self.price_command))
            self.application.add_handler(CommandHandler("analysis", self.analysis_command))
            self.application.add_handler(CommandHandler("strategies", self.strategies_command))
            self.application.add_handler(CommandHandler("start_trading", self.start_trading_command))
            self.application.add_handler(CommandHandler("stop_trading", self.stop_trading_command))
            
            logger.info("✅ Telegram bot handlers registered")
            
            # Start market analyzer in background
            logger.info("Starting market analyzer...")
            market_analysis_task = asyncio.create_task(self.market_analyzer.start_analysis())
            
            # Start the bot
            logger.info("🚀 Starting Telegram bot...")
            logger.info(f"Bot username: @{(await self.application.bot.get_me()).username}")
            
            try:
                await self.application.run_polling(allowed_updates=["message"])
            finally:
                # Cleanup
                logger.info("Shutting down bot...")
                self.market_analyzer.stop_analysis()
                self.strategy_manager.stop_automated_trading()
                await market_analysis_task
                
        except Exception as e:
            logger.error(f"Error running Telegram bot: {e}")
            logger.info("💡 Try using simple_bot.py instead for console interface")

async def main():
    """Main function"""
    # Setup logging
    logger.remove()
    logger.add(sys.stderr, level="INFO")
    logger.add("trading_bot.log", level="INFO", rotation="10 MB")
    
    bot = TelegramTradingBot()
    
    try:
        await bot.run()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
