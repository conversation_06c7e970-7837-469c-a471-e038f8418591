#!/usr/bin/env python3
"""
Test bestand voor Nederlandse AI assistant communicatie.

Dit bestand test of AI assistenten in het Nederlands reageren.
"""

def test_nederlandse_functie():
    """
    Deze functie test Nederlandse communicatie met AI assistenten.
    
    Returns:
        str: Een Nederland<PERSON> bevestiging
    """
    # Dit is een Nederlandse comment
    # AI assistenten zouden dit moeten herkennen en in het Nederlands antwoorden
    
    print("Hallo! Dit is een test voor Nederlandse AI communicatie.")
    
    # Vraag aan AI: Kun je deze functie uitleggen in het Nederlands?
    # Vraag aan AI: Wat doet deze code?
    # Vraag aan AI: Hoe kan ik dit verbeteren?
    
    return "Nederlandse test succesvol!"

def bereken_trading_winst(start_bedrag: float, eind_bedrag: float) -> float:
    """
    Bereken de winst van een trading positie.
    
    Args:
        start_bedrag: Het initiële bedrag
        eind_bedrag: Het finale bedrag
        
    Returns:
        float: De winst in percentage
    """
    # Bereken percentage winst
    winst_percentage = ((eind_bedrag - start_bedrag) / start_bedrag) * 100
    
    return winst_percentage

if __name__ == "__main__":
    # Test de Nederlandse functie
    resultaat = test_nederlandse_functie()
    print(f"Resultaat: {resultaat}")
    
    # Test trading winst berekening
    winst = bereken_trading_winst(100.0, 150.0)
    print(f"Trading winst: {winst:.2f}%")
    
    # Vraag aan AI: Kun je deze code uitleggen?
    # Vraag aan AI: Hoe werkt deze functie?
