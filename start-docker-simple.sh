#!/bin/bash

# Simple Docker Startup Script (without <PERSON>er Compose)
echo "🚀 Starting MyOwnMoneyMaker Trading Bot in Docker..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create .env file with your API keys."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs
mkdir -p data

# Stop any existing container
echo "🛑 Stopping existing container..."
docker stop myownmoneymaker-bot 2>/dev/null || true
docker rm myownmoneymaker-bot 2>/dev/null || true

# Build the Docker image
echo "🔨 Building Docker image..."
docker build -t myownmoneymaker-bot .

# Run the container
echo "🚀 Starting the trading bot container..."
docker run -d \
  --name myownmoneymaker-bot \
  --restart unless-stopped \
  --env-file .env \
  -v "$(pwd)/logs:/app/logs" \
  -v "$(pwd)/data:/app/data" \
  -e TZ=Europe/Amsterdam \
  myownmoneymaker-bot

# Check if container started successfully
if [ $? -eq 0 ]; then
    echo "✅ Trading bot is now running 24/7 in Docker!"
    echo ""
    echo "Container status:"
    docker ps | grep myownmoneymaker-bot
    echo ""
    echo "📝 Showing recent logs (press Ctrl+C to exit):"
    echo "To view logs later, run: docker logs -f myownmoneymaker-bot"
    echo ""
    docker logs -f myownmoneymaker-bot
else
    echo "❌ Failed to start the container"
    exit 1
fi

echo ""
echo "Useful commands:"
echo "  View logs:     docker logs -f myownmoneymaker-bot"
echo "  Stop bot:      docker stop myownmoneymaker-bot"
echo "  Restart bot:   docker restart myownmoneymaker-bot"
echo "  View status:   docker ps | grep myownmoneymaker-bot"
