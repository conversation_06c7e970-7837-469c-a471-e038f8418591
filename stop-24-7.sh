#!/bin/bash

# Stop 24/7 Trading Bot Script
echo "🛑 Stopping MyOwnMoneyMaker Trading Bot..."

# Find and kill the bot process
BOT_PIDS=$(pgrep -f "python.*telegram_simple.py")

if [ -n "$BOT_PIDS" ]; then
    echo "📋 Found bot processes: $BOT_PIDS"
    echo "🛑 Stopping processes..."
    pkill -f "python.*telegram_simple.py"
    sleep 2
    
    # Force kill if still running
    BOT_PIDS=$(pgrep -f "python.*telegram_simple.py")
    if [ -n "$BOT_PIDS" ]; then
        echo "🔥 Force killing remaining processes..."
        pkill -9 -f "python.*telegram_simple.py"
    fi
    
    echo "✅ Trading bot stopped successfully!"
else
    echo "ℹ️ No bot processes found running."
fi

echo ""
echo "To start again, run: ./start-24-7.sh"
