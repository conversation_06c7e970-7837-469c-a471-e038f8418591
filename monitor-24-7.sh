#!/bin/bash

# 24/7 Trading Bot Monitor Script
echo "🔍 MyOwnMoneyMaker Trading Bot Monitor"
echo "======================================"

# Function to check bot status
check_bot_status() {
    BOT_PIDS=$(pgrep -f "python.*telegram_simple.py")
    if [ -n "$BOT_PIDS" ]; then
        return 0  # <PERSON><PERSON> is running
    else
        return 1  # Bot is not running
    fi
}

# Function to restart bot if needed
restart_if_needed() {
    if ! check_bot_status; then
        echo "⚠️ Bot is not running! Attempting to restart..."
        ./start-24-7.sh
        sleep 10
        if check_bot_status; then
            echo "✅ Bot successfully restarted!"
        else
            echo "❌ Failed to restart bot!"
        fi
    fi
}

# Main monitoring loop
while true; do
    clear
    echo "🔍 MyOwnMoneyMaker Trading Bot Monitor"
    echo "======================================"
    echo "⏰ $(date)"
    echo ""
    
    if check_bot_status; then
        echo "✅ Bot Status: RUNNING"
        BOT_PIDS=$(pgrep -f "python.*telegram_simple.py")
        echo "📋 Process ID: $BOT_PIDS"
        
        # Show memory usage
        echo "💾 Memory Usage:"
        ps -o pid,ppid,%mem,%cpu,etime -p $BOT_PIDS 2>/dev/null || echo "Could not get process info"
        
        # Show recent activity
        echo ""
        echo "📝 Recent Activity (last 5 lines):"
        if [ -f "logs/bot.log" ]; then
            tail -5 logs/bot.log | sed 's/^/  /'
        else
            echo "  No log file found"
        fi
        
    else
        echo "❌ Bot Status: NOT RUNNING"
        echo "🔄 Attempting automatic restart..."
        restart_if_needed
    fi
    
    echo ""
    echo "🔧 Controls:"
    echo "  Press 'r' + Enter to restart bot"
    echo "  Press 's' + Enter to stop bot"
    echo "  Press 'l' + Enter to view full logs"
    echo "  Press 'q' + Enter to quit monitor"
    echo ""
    echo "⏱️ Auto-refresh in 30 seconds..."
    
    # Wait for user input or timeout
    read -t 30 -p "Command: " command
    
    case $command in
        r|R)
            echo "🔄 Restarting bot..."
            ./restart-24-7.sh
            sleep 5
            ;;
        s|S)
            echo "🛑 Stopping bot..."
            ./stop-24-7.sh
            sleep 3
            ;;
        l|L)
            echo "📝 Showing full logs..."
            tail -50 logs/bot.log
            read -p "Press Enter to continue..."
            ;;
        q|Q)
            echo "👋 Exiting monitor..."
            exit 0
            ;;
        "")
            # Timeout - continue loop
            ;;
        *)
            echo "❓ Unknown command: $command"
            sleep 2
            ;;
    esac
done
