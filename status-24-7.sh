#!/bin/bash

# Check 24/7 Trading Bot Status
echo "📊 MyOwnMoneyMaker Trading Bot Status"
echo "======================================"

# Check if bot is running
BOT_PIDS=$(pgrep -f "python.*telegram_simple.py")

if [ -n "$BOT_PIDS" ]; then
    echo "✅ Bot is RUNNING"
    echo "📋 Process IDs: $BOT_PIDS"
    echo ""
    echo "📊 Process details:"
    ps aux | grep "python.*telegram_simple.py" | grep -v grep
    echo ""
    echo "💾 Memory usage:"
    ps -o pid,ppid,cmd,%mem,%cpu -p $BOT_PIDS
    echo ""
    echo "📝 Recent logs (last 10 lines):"
    if [ -f "logs/bot.log" ]; then
        tail -10 logs/bot.log
    else
        echo "No log file found."
    fi
else
    echo "❌ Bot is NOT RUNNING"
    echo ""
    echo "📝 Last log entries (if available):"
    if [ -f "logs/bot.log" ]; then
        tail -20 logs/bot.log
    else
        echo "No log file found."
    fi
fi

echo ""
echo "Available commands:"
echo "  Start bot:     ./start-24-7.sh"
echo "  Stop bot:      ./stop-24-7.sh"
echo "  Restart bot:   ./restart-24-7.sh"
echo "  View logs:     tail -f logs/bot.log"
