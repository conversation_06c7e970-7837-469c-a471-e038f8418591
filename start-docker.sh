#!/bin/bash

# Trading Bot Docker Startup Script
echo "🚀 Starting MyOwnMoneyMaker Trading Bot in Docker..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Please create .env file with your API keys."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs
mkdir -p data

# Stop any existing containers
echo "🛑 Stopping existing containers..."
docker-compose down

# Build and start the container
echo "🔨 Building and starting the trading bot..."
docker-compose up --build -d

# Show container status
echo "📊 Container status:"
docker-compose ps

# Show logs
echo "📝 Showing recent logs (press Ctrl+C to exit):"
echo "To view logs later, run: docker-compose logs -f"
echo ""
docker-compose logs -f

echo "✅ Trading bot is now running 24/7 in Docker!"
echo ""
echo "Useful commands:"
echo "  View logs:     docker-compose logs -f"
echo "  Stop bot:      docker-compose down"
echo "  Restart bot:   docker-compose restart"
echo "  View status:   docker-compose ps"
