# Telegram Bot
python-telegram-bot==20.8

# Exchange APIs
ccxt==4.4.76

# Environment variables
python-dotenv>=1.0.0

# Async support
aiohttp==3.9.1

# HTTP client - compatible version
httpx==0.26.0

# Logging
loguru==0.7.2

# Data Analysis & Technical Indicators
pandas>=2.0.0
pandas_ta>=0.3.14b

# Security
cryptography>=41.0.0

# Additional Dependencies (if needed)
tweepy>=4.14.0
praw>=7.7.1
newsapi-python>=0.2.7

# Note: Removed langchain-openai to avoid conflicts
# We'll make the AI analyzer optional
