#!/usr/bin/env python3
"""
Test script voor button response snelheid
"""

import asyncio
import aiohttp
import ssl
import time
import json
from dotenv import load_dotenv
import os

async def test_telegram_api_speed():
    """Test Telegram API response snelheid"""

    load_dotenv()
    bot_token = os.getenv('TELEGRAM_BOT_TOKEN')

    if not bot_token:
        print("❌ Geen bot token gevonden")
        return

    base_url = f"https://api.telegram.org/bot{bot_token}"

    # Optimized session zoals in de bot
    ssl_context = ssl.create_default_context()
    connector = aiohttp.TCPConnector(
        ssl=ssl_context,
        limit=100,
        limit_per_host=30,
        ttl_dns_cache=300,
        use_dns_cache=True,
        keepalive_timeout=30,
        enable_cleanup_closed=True
    )

    timeout = aiohttp.ClientTimeout(
        total=10,
        connect=3,
        sock_read=5
    )

    async with aiohttp.ClientSession(
        connector=connector,
        timeout=timeout,
        headers={'User-Agent': 'TradingBot/1.0'}
    ) as session:

        print("🚀 Testing Telegram API Response Speed...")
        print("=" * 50)

        # Test 1: getMe (simple API call)
        start_time = time.time()
        try:
            async with session.get(f"{base_url}/getMe") as response:
                if response.status == 200:
                    data = await response.json()
                    end_time = time.time()
                    print(f"✅ getMe: {(end_time - start_time)*1000:.0f}ms")
                else:
                    print(f"❌ getMe failed: {response.status}")
        except Exception as e:
            print(f"❌ getMe error: {e}")

        # Test 2: Multiple rapid calls
        print("\n🔄 Testing rapid API calls...")
        times = []

        for i in range(5):
            start_time = time.time()
            try:
                async with session.get(f"{base_url}/getMe") as response:
                    if response.status == 200:
                        end_time = time.time()
                        response_time = (end_time - start_time) * 1000
                        times.append(response_time)
                        print(f"  Call {i+1}: {response_time:.0f}ms")
            except Exception as e:
                print(f"  Call {i+1}: ERROR - {e}")

            await asyncio.sleep(0.1)  # Small delay

        if times:
            avg_time = sum(times) / len(times)
            print(f"\n📊 Average response time: {avg_time:.0f}ms")
            print(f"📊 Min: {min(times):.0f}ms, Max: {max(times):.0f}ms")

            if avg_time < 500:
                print("✅ Response time is GOOD (< 500ms)")
            elif avg_time < 1000:
                print("⚠️ Response time is OK (500-1000ms)")
            else:
                print("❌ Response time is SLOW (> 1000ms)")

async def test_callback_simulation():
    """Simuleer callback processing snelheid"""

    print("\n🎯 Testing Callback Processing Speed...")
    print("=" * 50)

    # Simuleer verschillende callback types
    callbacks = [
        "menu_portfolio",
        "menu_trading",
        "primary_trading",
        "select_strategy",
        "set_trading_amount",
        "portfolio_balance",
        "market_analysis"
    ]

    processing_times = []

    for callback in callbacks:
        start_time = time.time()

        # Simuleer callback processing
        await asyncio.sleep(0.01)  # Simulate minimal processing

        end_time = time.time()
        processing_time = (end_time - start_time) * 1000
        processing_times.append(processing_time)

        print(f"  {callback}: {processing_time:.1f}ms")

    avg_processing = sum(processing_times) / len(processing_times)
    print(f"\n📊 Average callback processing: {avg_processing:.1f}ms")

def print_optimization_tips():
    """Print optimization tips"""

    print("\n🔧 OPTIMIZATION TIPS IMPLEMENTED:")
    print("=" * 50)
    print("✅ Immediate callback acknowledgment")
    print("✅ Duplicate callback prevention")
    print("✅ Balance caching (30 seconds)")
    print("✅ Optimized HTTP session")
    print("✅ Connection pooling")
    print("✅ DNS caching")
    print("✅ Keep-alive connections")
    print("✅ Proper timeouts")
    print("✅ Loading indicators for slow operations")

    print("\n💡 ADDITIONAL RECOMMENDATIONS:")
    print("=" * 50)
    print("• Use /start command to refresh bot")
    print("• Clear Telegram cache if buttons still slow")
    print("• Check internet connection speed")
    print("• Restart bot if memory usage high")
    print("• Monitor logs for errors")

async def main():
    """Main test function"""

    print("⚡ TELEGRAM BOT SPEED TEST")
    print("=" * 50)

    await test_telegram_api_speed()
    await test_callback_simulation()
    print_optimization_tips()

    print("\n🎉 Speed test complete!")
    print("If buttons are still slow, try:")
    print("1. Restart the bot: ./restart-24-7.sh")
    print("2. Clear Telegram app cache")
    print("3. Check network connection")

if __name__ == "__main__":
    asyncio.run(main())
