#!/bin/bash
echo "🔧 Fixing SSL Certificate Issue..."

# Fix SSL certificates for macOS
pip install --upgrade certifi

# Get Python path
PYTHON_PATH=$(python3 -c "import sys; print(sys.executable)")
echo "Python path: $PYTHON_PATH"

# Install certificates (macOS specific)
if [[ "$OSTYPE" == "darwin"* ]]; then
    echo "📱 macOS detected - installing certificates..."
    
    # Try to run the Install Certificates command
    CERT_SCRIPT="/Applications/Python 3.13/Install Certificates.command"
    if [ -f "$CERT_SCRIPT" ]; then
        echo "Running certificate installer..."
        bash "$CERT_SCRIPT"
    else
        echo "Certificate installer not found at default location"
        # Alternative method
        pip install --upgrade certifi
        python3 -m certifi > /dev/null 2>&1
    fi
fi

# Alternative fix - set environment variable
export SSL_CERT_FILE=$(python3 -m certifi)
export REQUESTS_CA_BUNDLE=$(python3 -m certifi)

echo "✅ SSL fix applied!"
echo ""
echo "Now starting bot with SSL fix..."

# Start bot with SSL environment variables
SSL_CERT_FILE=$(python3 -m certifi) REQUESTS_CA_BUNDLE=$(python3 -m certifi) python3 telegram_simple.py
