#!/usr/bin/env python3
"""
Test om te checken of alle imports werken
"""
import sys
print(f"Python version: {sys.version}")

print("\n🧪 Testing imports...")

try:
    import telegram
    print("✅ telegram module loaded")
except ImportError as e:
    print(f"❌ telegram import failed: {e}")

try:
    import ccxt
    print("✅ ccxt module loaded")
except ImportError as e:
    print(f"❌ ccxt import failed: {e}")

try:
    import pandas
    print("✅ pandas module loaded")
except ImportError as e:
    print(f"❌ pandas import failed: {e}")

try:
    from config import get_settings
    settings = get_settings()
    print("✅ config loaded")
    print(f"   Telegram token: {settings.telegram_bot_token[:10]}...")
except ImportError as e:
    print(f"❌ config import failed: {e}")

try:
    from exchanges.manager import ExchangeManager
    print("✅ ExchangeManager loaded")
except ImportError as e:
    print(f"⚠️  ExchangeManager import failed: {e}")

try:
    from analysis.ai_analyzer import AIAnalyzer
    print("✅ AIAnalyzer loaded")
except ImportError as e:
    print(f"⚠️  AIAnalyzer import failed: {e}")

print("\n✅ Import test complete!")
