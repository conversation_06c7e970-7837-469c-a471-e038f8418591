{
    // ECHTE VSCode instellingen die werken
    "$schema": "http://json-schema.org/draft-07/schema#",
    
    // Python instellingen
    "python.analysis.extraPaths": [
        "telegram_utils"
    ],
    
    // Augment instellingen (bestaande)
    "augment.advanced": {
        "useLocal": true,
        "local": {
            "provider": "openai",
            "apiBase": "http://127.0.0.1:1234/v1",
            "apiKey": "lm-studio",
            "models": {
                "default": "deepseek-coder-33b-instruct",
                "available": [
                    "deepseek-coder-33b-instruct",
                    "codellama-70b",
                    "mixtral-8x7b-instruct",
                    "neural-chat-7b",
                    "phi-2"
                ]
            },
            "features": {
                "chat": true,
                "debug": true,
                "codeCompletion": true,
                "testing": true,
                "documentation": true,
                "refactoring": true
            }
        }
    },
    
    // Editor instellingen
    "editor.formatOnSave": true,
    "editor.wordWrap": "on",
    "editor.fontSize": 14,
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    
    // Python linting
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "ruff.lint.fixOnSave": true,
    
    // Augment edit instellingen
    "augment.nextEdit.enableAutoApply": true,
    "augment.nextEdit.deleteStrategy": "disabled",
    "augment.nextEdit.enableBackgroundSuggestions": false,
    "augment.nextEdit.enableGlobalBackgroundSuggestions": false,
    
    // Workbench instellingen
    "workbench.editor.autoLockGroups": {
        "default": true
    },
    
    // ECHTE taal instellingen die werken
    "locale": "nl",
    
    // Spell checker (als je Code Spell Checker extensie hebt)
    "cSpell.language": "en,nl",
    "cSpell.enableFiletypes": [
        "markdown",
        "text",
        "plaintext",
        "python"
    ],
    
    // GitHub Copilot (als je het hebt)
    "github.copilot.enable": {
        "*": true,
        "yaml": false,
        "plaintext": true,
        "markdown": true
    },
    
    // Terminal instellingen
    "terminal.integrated.defaultProfile.osx": "bash",
    "terminal.integrated.fontSize": 13,
    
    // Bestand instellingen
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    
    // Git instellingen
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    
    // Zoek instellingen
    "search.exclude": {
        "**/node_modules": true,
        "**/bower_components": true,
        "**/*.code-search": true,
        "**/logs": true,
        "**/__pycache__": true,
        "**/.git": true,
        "**/venv": true,
        "**/.env": true
    },
    
    // Extensie instellingen
    "extensions.autoUpdate": true,
    "extensions.autoCheckUpdates": true
}
