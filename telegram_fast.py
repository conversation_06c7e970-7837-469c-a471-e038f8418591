#!/usr/bin/env python3
"""
Fast Telegram Trading Bot - Optimized for Quick Trading
"""

import asyncio
import time
from decimal import Decimal
from typing import Dict, List, Optional
from loguru import logger

# Import directly from root config.py
import importlib.util
import os
spec = importlib.util.spec_from_file_location("root_config", "config.py")
root_config = importlib.util.module_from_spec(spec)
spec.loader.exec_module(root_config)

from exchanges.manager import ExchangeManager

class FastTradingBot:
    """Fast trading bot without heavy analysis"""

    def __init__(self):
        self.settings = root_config.get_settings()
        self.exchange_manager = ExchangeManager()
        self.running = False

    async def initialize(self):
        """Initialize the bot"""
        logger.info("🚀 Initializing Fast Trading Bot...")

        # Connect to exchanges
        connections = await self.exchange_manager.connect_all()
        for exchange, connected in connections.items():
            if connected:
                logger.info(f"✅ Connected to {exchange}")
            else:
                logger.error(f"❌ Failed to connect to {exchange}")

        logger.info("✅ Fast bot initialized")

    async def get_price(self, symbol: str) -> Dict:
        """Get current price for a symbol"""
        try:
            tickers = await self.exchange_manager.get_ticker_from_all(symbol)
            if not tickers:
                return {"error": f"No price data for {symbol}"}

            result = {}
            for exchange, ticker in tickers.items():
                result[exchange] = {
                    "price": float(ticker.last),
                    "bid": float(ticker.bid),
                    "ask": float(ticker.ask),
                    "volume": float(ticker.volume)
                }

            return result

        except Exception as e:
            logger.error(f"Error getting price for {symbol}: {e}")
            return {"error": str(e)}

    async def get_balance(self) -> Dict:
        """Get balances from all exchanges"""
        try:
            all_balances = await self.exchange_manager.get_all_balances()
            result = {}

            for exchange, balances in all_balances.items():
                result[exchange] = {}
                for currency, balance in balances.items():
                    if balance.total > 0:
                        result[exchange][currency] = {
                            "total": float(balance.total),
                            "free": float(balance.free),
                            "used": float(balance.used)
                        }

            return result

        except Exception as e:
            logger.error(f"Error getting balances: {e}")
            return {"error": str(e)}

    async def create_buy_order(self, symbol: str, amount: float, exchange: str = None) -> Dict:
        """Create a buy order"""
        try:
            if exchange:
                # Use specific exchange
                order = await self.exchange_manager.create_order(
                    exchange_name=exchange,
                    order_type="market",
                    side="buy",
                    symbol=symbol,
                    amount=Decimal(str(amount))
                )
                return {"success": True, "order_id": order.id, "exchange": exchange}
            else:
                # Use KuCoin by default (more reliable)
                order = await self.exchange_manager.create_order(
                    exchange_name="kucoin",
                    order_type="market",
                    side="buy",
                    symbol=symbol,
                    amount=Decimal(str(amount))
                )
                return {
                    "success": True,
                    "order_id": order.id,
                    "exchange": "kucoin"
                }

        except Exception as e:
            logger.error(f"Error creating buy order: {e}")
            return {"error": str(e)}

    async def create_sell_order(self, symbol: str, amount: float, exchange: str = None) -> Dict:
        """Create a sell order"""
        try:
            if exchange:
                # Use specific exchange
                order = await self.exchange_manager.create_order(
                    exchange_name=exchange,
                    order_type="market",
                    side="sell",
                    symbol=symbol,
                    amount=Decimal(str(amount))
                )
                return {"success": True, "order_id": order.id, "exchange": exchange}
            else:
                # Use KuCoin by default (more reliable)
                order = await self.exchange_manager.create_order(
                    exchange_name="kucoin",
                    order_type="market",
                    side="sell",
                    symbol=symbol,
                    amount=Decimal(str(amount))
                )
                return {
                    "success": True,
                    "order_id": order.id,
                    "exchange": "kucoin"
                }

        except Exception as e:
            logger.error(f"Error creating sell order: {e}")
            return {"error": str(e)}

# Simple command line interface
async def main():
    bot = FastTradingBot()
    await bot.initialize()

    print("\n🚀 Fast Trading Bot Ready!")
    print("Commands:")
    print("  price BTC/USDT - Get price")
    print("  balance - Get balances")
    print("  buy BTC/USDT 5 - Buy $5 of BTC")
    print("  sell BTC/USDT 0.001 - Sell 0.001 BTC")
    print("  quit - Exit")

    while True:
        try:
            command = input("\n> ").strip().split()
            if not command:
                continue

            if command[0] == "quit":
                break
            elif command[0] == "price" and len(command) > 1:
                result = await bot.get_price(command[1])
                print(f"📊 {command[1]} prices:")
                for exchange, data in result.items():
                    if exchange != "error":
                        print(f"  {exchange}: ${data['price']:.8f}")
                    else:
                        print(f"  Error: {data}")

            elif command[0] == "balance":
                result = await bot.get_balance()
                print("💰 Balances:")
                for exchange, balances in result.items():
                    if exchange != "error":
                        print(f"  {exchange}:")
                        for currency, data in balances.items():
                            print(f"    {currency}: {data['total']:.8f}")
                    else:
                        print(f"  Error: {balances}")

            elif command[0] == "buy" and len(command) >= 3:
                symbol = command[1]
                amount = float(command[2])
                result = await bot.create_buy_order(symbol, amount)
                if result.get("success"):
                    print(f"✅ Buy order created: {result['order_id']} on {result['exchange']}")
                else:
                    print(f"❌ Error: {result.get('error')}")

            elif command[0] == "sell" and len(command) >= 3:
                symbol = command[1]
                amount = float(command[2])
                result = await bot.create_sell_order(symbol, amount)
                if result.get("success"):
                    print(f"✅ Sell order created: {result['order_id']} on {result['exchange']}")
                else:
                    print(f"❌ Error: {result.get('error')}")

            else:
                print("❌ Unknown command")

        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ Error: {e}")

    print("👋 Goodbye!")

if __name__ == "__main__":
    asyncio.run(main())
