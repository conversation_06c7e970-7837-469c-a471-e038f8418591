#!/usr/bin/env python3
"""
Quick test script for Telegram bot functionality
"""
import asyncio
import aiohttp
import os
from dotenv import load_dotenv

load_dotenv()

async def test_bot_quick():
    """Quick test of bot functionality"""

    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    admin_ids = os.getenv("TELEGRAM_ADMIN_USER_ID", "").split(",")
    # Use the second admin ID (6229184945) since the first one is a bot
    admin_id = admin_ids[1] if len(admin_ids) > 1 else admin_ids[0]

    if not bot_token or not admin_id:
        print("❌ Missing bot token or admin ID")
        return

    base_url = f"https://api.telegram.org/bot{bot_token}"

    async with aiohttp.ClientSession() as session:
        # Test 1: Bot info
        print("🔍 Testing bot info...")
        async with session.get(f"{base_url}/getMe") as response:
            data = await response.json()
            if data.get('ok'):
                bot_info = data['result']
                print(f"✅ Bot: @{bot_info['username']}")
            else:
                print(f"❌ Bot info failed: {data}")
                return

        # Test 2: Get updates with short timeout
        print("🔍 Testing updates (short timeout)...")
        try:
            timeout = aiohttp.ClientTimeout(total=5)
            async with session.get(f"{base_url}/getUpdates",
                                 params={"timeout": 2},
                                 timeout=timeout) as response:
                data = await response.json()
                if data.get('ok'):
                    updates = data['result']
                    print(f"✅ Updates: {len(updates)} messages")

                    if updates:
                        last_update = updates[-1]
                        print(f"📍 Last update ID: {last_update['update_id']}")
                else:
                    print(f"❌ Updates failed: {data}")
        except asyncio.TimeoutError:
            print("⏰ Updates timeout (normal for short timeout)")
        except Exception as e:
            print(f"❌ Updates error: {e}")

        # Test 3: Send /start command to trigger bot response
        print(f"🔍 Testing /start command to {admin_id}...")
        test_message = "/start"

        payload = {
            "chat_id": admin_id,
            "text": test_message
        }

        try:
            async with session.post(f"{base_url}/sendMessage", json=payload) as response:
                data = await response.json()
                if data.get('ok'):
                    print("✅ /start command sent successfully!")
                    print("🔍 Waiting 3 seconds for bot response...")
                    await asyncio.sleep(3)

                    # Check for bot response
                    async with session.get(f"{base_url}/getUpdates",
                                         params={"timeout": 1}) as response:
                        data = await response.json()
                        if data.get('ok') and data['result']:
                            print(f"📨 Bot processed {len(data['result'])} updates")
                        else:
                            print("⚠️ No bot response detected")
                else:
                    print(f"❌ Send /start failed: {data}")
        except Exception as e:
            print(f"❌ Send /start error: {e}")

if __name__ == "__main__":
    asyncio.run(test_bot_quick())
