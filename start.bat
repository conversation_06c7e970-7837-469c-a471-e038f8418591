@echo off
REM Trading Bot Startup Script voor Windows

echo 🤖 Trading Bot Startup
echo ======================

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "config.py" (
    echo ❌ Not in the correct directory. Please run this from the Myownmoneymaker folder.
    pause
    exit /b 1
)

REM Install dependencies if needed
echo 📦 Installing dependencies...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

REM Check .env file
if not exist ".env" (
    echo ❌ .env file not found!
    echo Please create a .env file with your API keys.
    pause
    exit /b 1
)

echo ✅ Setup complete!
echo.

REM Ask user which bot to start
echo 🎯 Choose bot type:
echo 1. <PERSON><PERSON><PERSON> (Terminal interface)
echo 2. Telegram Bot (Mobile interface)
echo 3. Simple Test (Check connections)
echo.

set /p choice="Enter choice (1, 2, or 3): "

if "%choice%"=="1" (
    echo 🚀 Starting Console Bot...
    python simple_bot.py
) else if "%choice%"=="2" (
    echo 🚀 Starting Telegram Bot...
    python telegram_simple.py
) else if "%choice%"=="3" (
    echo 🧪 Running connection test...
    python test_exchanges.py
) else (
    echo ❌ Invalid choice. Starting console bot...
    python simple_bot.py
)

pause
